# Deployment Guide - Arabic E-commerce Website

## 🚀 Production Deployment

### Prerequisites
- Node.js 18+ installed
- Supabase account and project
- Domain name (optional)
- SSL certificate (for production)

### Environment Setup

#### 1. Frontend Environment Variables
Create `frontend/.env.production`:
```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_API_URL=https://your-domain.com/api
VITE_APP_NAME=متجر شغف
VITE_APP_DESCRIPTION=متجر إلكتروني عربي متخصص
```

#### 2. Backend Environment Variables
Create `backend/.env.production`:
```env
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_KEY=your_supabase_service_role_key
FLASK_ENV=production
SECRET_KEY=your_super_secret_key_here
CORS_ORIGINS=https://your-domain.com
```

### Database Setup

#### 1. Supabase Configuration
```sql
-- Enable Row Level Security on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE cart_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (already implemented in schema)
-- Policies are defined in database/schema.sql
```

#### 2. Initial Data Setup
```sql
-- Insert default categories
INSERT INTO categories (name_ar, name_en, description_ar, description_en) VALUES
('إلكترونيات', 'Electronics', 'أجهزة إلكترونية متنوعة', 'Various electronic devices'),
('ملابس', 'Clothing', 'ملابس رجالية ونسائية', 'Men and women clothing'),
('منزل وحديقة', 'Home & Garden', 'مستلزمات المنزل والحديقة', 'Home and garden supplies');

-- Insert sample products
INSERT INTO products (name_ar, name_en, description_ar, description_en, price, category_id, stock_quantity, images) VALUES
('هاتف ذكي', 'Smartphone', 'هاتف ذكي بمواصفات عالية', 'High-spec smartphone', 999.00, 1, 50, ARRAY['/images/phone1.jpg']),
('قميص قطني', 'Cotton Shirt', 'قميص قطني عالي الجودة', 'High-quality cotton shirt', 89.00, 2, 100, ARRAY['/images/shirt1.jpg']);
```

### Build and Deployment

#### 1. Frontend Build
```bash
cd frontend
npm install
npm run build
```

#### 2. Backend Setup
```bash
cd backend
pip install -r requirements.txt
```

#### 3. Static File Hosting
Upload the `frontend/dist` folder to your hosting provider:
- **Vercel**: Connect GitHub repo and deploy
- **Netlify**: Drag and drop dist folder
- **AWS S3**: Upload to S3 bucket with CloudFront
- **Traditional hosting**: Upload to public_html

#### 4. Backend Deployment
Deploy Flask backend to:
- **Heroku**: `git push heroku main`
- **Railway**: Connect GitHub repo
- **DigitalOcean App Platform**: Connect repo
- **AWS EC2**: Use PM2 or systemd

### Production Configuration

#### 1. Nginx Configuration (if using VPS)
```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    # Frontend
    location / {
        root /var/www/ecommerce/frontend/dist;
        try_files $uri $uri/ /index.html;
    }
    
    # Backend API
    location /api {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

#### 2. PM2 Configuration (for backend)
```json
{
  "name": "ecommerce-api",
  "script": "app.py",
  "interpreter": "python3",
  "env": {
    "FLASK_ENV": "production"
  },
  "instances": 2,
  "exec_mode": "cluster"
}
```

### Security Checklist

#### 1. Environment Security
- [x] All sensitive data in environment variables
- [x] No hardcoded secrets in code
- [x] Secure random SECRET_KEY
- [x] HTTPS enforced in production
- [x] CORS properly configured

#### 2. Database Security
- [x] Row Level Security enabled
- [x] Proper user permissions
- [x] SQL injection prevention
- [x] Input validation and sanitization

#### 3. Authentication Security
- [x] Secure password policies
- [x] JWT token expiration
- [x] Session management
- [x] Rate limiting (recommended)

### Performance Optimization

#### 1. Frontend Optimization
- [x] Code splitting implemented
- [x] Lazy loading for routes
- [x] Image optimization
- [x] CSS minification
- [x] JavaScript minification
- [x] Gzip compression

#### 2. Backend Optimization
- [x] Database query optimization
- [x] Caching strategies
- [x] API response compression
- [x] Connection pooling

#### 3. CDN Configuration
```javascript
// Configure CDN for static assets
const CDN_URL = 'https://cdn.your-domain.com';
// Update image URLs to use CDN
```

### Monitoring and Analytics

#### 1. Error Tracking
```javascript
// Sentry configuration
import * as Sentry from "@sentry/react";

Sentry.init({
  dsn: "YOUR_SENTRY_DSN",
  environment: "production"
});
```

#### 2. Analytics
```javascript
// Google Analytics 4
gtag('config', 'GA_MEASUREMENT_ID');
```

#### 3. Performance Monitoring
```javascript
// Web Vitals tracking
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

getCLS(console.log);
getFID(console.log);
getFCP(console.log);
getLCP(console.log);
getTTFB(console.log);
```

### Backup and Recovery

#### 1. Database Backup
```bash
# Automated daily backups via Supabase
# Manual backup command
pg_dump -h your-supabase-host -U postgres your-db > backup.sql
```

#### 2. Code Backup
- [x] Git repository with regular commits
- [x] Multiple remote repositories
- [x] Tagged releases for versions

### Testing in Production

#### 1. Smoke Tests
```bash
# Test critical paths
curl -f https://your-domain.com/api/health
curl -f https://your-domain.com/api/products
```

#### 2. Load Testing
```bash
# Use tools like Apache Bench or Artillery
ab -n 1000 -c 10 https://your-domain.com/
```

### Maintenance

#### 1. Regular Updates
- [ ] Weekly dependency updates
- [ ] Monthly security patches
- [ ] Quarterly feature releases

#### 2. Monitoring
- [ ] Daily error log review
- [ ] Weekly performance analysis
- [ ] Monthly user analytics review

### Rollback Plan

#### 1. Quick Rollback
```bash
# Frontend rollback
git checkout previous-stable-tag
npm run build
# Deploy previous build

# Backend rollback
git checkout previous-stable-tag
# Restart backend service
```

#### 2. Database Rollback
```sql
-- Restore from backup if needed
psql -h your-supabase-host -U postgres your-db < backup.sql
```

## 🎯 Go-Live Checklist

### Pre-Launch
- [x] All features tested and working
- [x] Performance optimized
- [x] Security measures implemented
- [x] SSL certificate installed
- [x] Domain configured
- [x] Analytics setup
- [x] Error tracking configured
- [x] Backup systems in place

### Launch Day
- [ ] Deploy to production
- [ ] Run smoke tests
- [ ] Monitor error logs
- [ ] Check performance metrics
- [ ] Verify all functionality
- [ ] Announce launch

### Post-Launch
- [ ] Monitor user feedback
- [ ] Track performance metrics
- [ ] Address any issues
- [ ] Plan future enhancements

## 📞 Support and Maintenance

### Contact Information
- **Developer**: [Your Name]
- **Email**: [<EMAIL>]
- **Documentation**: This repository
- **Issue Tracking**: GitHub Issues

### Maintenance Schedule
- **Daily**: Error monitoring
- **Weekly**: Performance review
- **Monthly**: Security updates
- **Quarterly**: Feature updates

The Arabic e-commerce website is now ready for production deployment! 🚀
