# متجر إلكتروني احترافي - Professional E-commerce Website

## نظرة عامة - Overview
موقع تجارة إلكترونية احترافي مطور باستخدام أحدث التقنيات مع دعم كامل للغة العربية.

A professional e-commerce website built with modern technologies and full Arabic language support.

## التقنيات المستخدمة - Tech Stack

### Frontend
- React with Vite
- Modern CSS with RTL support
- Responsive design
- Arabic localization

### Backend
- Python Flask
- RESTful API
- Authentication & Authorization
- Admin dashboard

### Database
- Supabase (Cloud database)
- Local browser storage for caching

## الميزات - Features

### للمستخدمين - User Features
- تصفح المنتجات - Product browsing
- البحث والتصفية - Search and filtering
- سلة التسوق - Shopping cart
- إدارة الحساب - Account management
- تتبع الطلبات - Order tracking

### للإدارة - Admin Features
- إدارة المنتجات - Product management
- إدارة الطلبات - Order management
- إدارة المستخدمين - User management
- التقارير والإحصائيات - Reports and analytics

## هيكل المشروع - Project Structure

```
شغف/
├── frontend/          # React frontend application
├── backend/           # Flask backend API
├── database/          # Database schemas and migrations
├── docs/             # Documentation
└── tests/            # Test files
```

## التطوير - Development

### متطلبات النظام - System Requirements
- Node.js 18+
- Python 3.9+
- Git

### التثبيت - Installation
```bash
# Clone the repository
git clone <repository-url>
cd شغف

# Install frontend dependencies
cd frontend
npm install

# Install backend dependencies
cd ../backend
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
```

### تشغيل المشروع - Running the Project
```bash
# Start frontend development server
cd frontend
npm run dev

# Start backend server
cd backend
python app.py
```

## الترخيص - License
This project is licensed under the MIT License.
