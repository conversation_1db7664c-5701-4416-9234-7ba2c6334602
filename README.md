# متجر شغف - Arabic E-commerce Website

A comprehensive, professional Arabic e-commerce platform built with modern web technologies. This project features a complete online shopping experience with Arabic-first design, RTL layout, and full e-commerce functionality.

## 🌟 Features

### 🛍️ Customer Features
- **Arabic-First Design**: Complete RTL layout with Arabic fonts and localization
- **Product Catalog**: Browse products with advanced search and filtering
- **Shopping Cart**: Add, update, and manage cart items with real-time calculations
- **Secure Checkout**: Multi-step checkout process with shipping and payment options
- **User Accounts**: Registration, login, profile management
- **Order History**: Track and view past orders
- **Responsive Design**: Optimized for mobile, tablet, and desktop

### 👨‍💼 Admin Features
- **Dashboard Analytics**: Comprehensive statistics and insights
- **Product Management**: Add, edit, and manage product catalog
- **Order Management**: Process and track customer orders
- **User Management**: Manage customer accounts and permissions
- **Category Management**: Organize products into categories
- **Inventory Tracking**: Monitor stock levels and low inventory alerts

### 🔧 Technical Features
- **Modern Tech Stack**: React 18, Flask, Supabase
- **Real-time Updates**: Live cart updates and notifications
- **Security**: JWT authentication, Row Level Security, input validation
- **Performance**: Optimized loading, caching, and responsive design
- **Testing**: Comprehensive test suite with Jest and React Testing Library
- **Deployment Ready**: Production configuration and deployment guides

## 🚀 Tech Stack

### Frontend
- **React 18.2.0** - Modern UI library with hooks
- **Vite** - Fast build tool and development server
- **React Router** - Client-side routing
- **React Query** - Data fetching and caching
- **Axios** - HTTP client for API requests
- **React Hook Form** - Form handling and validation
- **React Hot Toast** - Notifications and alerts
- **Lucide React** - Modern icon library

### Backend
- **Flask 3.0.0** - Python web framework
- **Supabase** - Backend-as-a-Service with PostgreSQL
- **PostgreSQL** - Relational database with advanced features
- **JWT** - JSON Web Tokens for authentication
- **CORS** - Cross-origin resource sharing

### Database
- **PostgreSQL** - Primary database
- **Row Level Security** - Database-level security policies
- **UUID** - Primary keys for better security
- **Triggers** - Automatic timestamp management

## 📁 Project Structure

```
شغف/
├── frontend/                 # React frontend application
│   ├── src/
│   │   ├── components/      # Reusable UI components
│   │   │   ├── Layout/      # Header, Footer, Layout components
│   │   │   ├── UI/          # Button, Input, Modal components
│   │   │   └── Product/     # Product-specific components
│   │   ├── pages/           # Page components
│   │   │   ├── Home.jsx     # Homepage
│   │   │   ├── Products.jsx # Product listing
│   │   │   ├── Cart.jsx     # Shopping cart
│   │   │   ├── Checkout.jsx # Checkout process
│   │   │   └── AdminDashboard.jsx # Admin panel
│   │   ├── contexts/        # React Context providers
│   │   │   ├── AuthContext.jsx    # Authentication state
│   │   │   └── CartContext.jsx    # Shopping cart state
│   │   ├── services/        # API service layer
│   │   │   └── api.js       # Centralized API calls
│   │   ├── styles/          # CSS stylesheets
│   │   │   └── main.css     # Main stylesheet
│   │   └── tests/           # Test files
│   ├── public/              # Static assets
│   └── package.json         # Dependencies and scripts
├── backend/                 # Flask backend API
│   ├── routes/              # API route handlers
│   │   ├── auth.py          # Authentication endpoints
│   │   ├── products.py      # Product management
│   │   ├── cart.py          # Shopping cart operations
│   │   ├── orders.py        # Order processing
│   │   └── admin.py         # Admin operations
│   ├── utils/               # Utility functions
│   │   ├── auth.py          # Authentication helpers
│   │   └── validation.py    # Input validation
│   ├── app.py               # Flask application entry point
│   └── requirements.txt     # Python dependencies
├── database/                # Database schema and setup
│   ├── schema.sql           # Complete database schema
│   └── sample_data.sql      # Sample data for testing
├── docs/                    # Documentation
│   ├── API.md               # API documentation
│   └── FEATURES.md          # Feature specifications
├── DEPLOYMENT_GUIDE.md      # Production deployment guide
├── QA_CHECKLIST.md          # Quality assurance checklist
└── README.md                # This file
```

## 🛠️ Installation & Setup

### Prerequisites
- Node.js 18+
- Python 3.9+
- Supabase account

### 1. Clone the Repository
```bash
git clone <repository-url>
cd شغف
```

### 2. Frontend Setup
```bash
cd frontend
npm install
```

Create `frontend/.env`:
```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
VITE_API_URL=http://localhost:5000/api
```

### 3. Backend Setup
```bash
cd backend
pip install -r requirements.txt
```

Create `backend/.env`:
```env
SUPABASE_URL=your_supabase_project_url
SUPABASE_SERVICE_KEY=your_supabase_service_role_key
FLASK_ENV=development
SECRET_KEY=your_secret_key_here
```

### 4. Database Setup
1. Create a new Supabase project
2. Run the SQL schema from `database/schema.sql`
3. Optionally load sample data from `database/sample_data.sql`

### 5. Run the Application

Start the backend:
```bash
cd backend
python app.py
```

Start the frontend:
```bash
cd frontend
npm run dev
```

Visit `http://localhost:5173` to see the application.

## 🧪 Testing

### Run Frontend Tests
```bash
cd frontend
npm test                    # Run tests once
npm run test:watch         # Run tests in watch mode
npm run test:coverage      # Run tests with coverage report
```

### Test Coverage
The project maintains high test coverage across:
- Component rendering and functionality
- User authentication flows
- Shopping cart operations
- API integration
- Error handling
- Responsive design

## 📚 Documentation

- **[API Documentation](docs/API.md)** - Complete API endpoint reference
- **[Feature Specifications](docs/FEATURES.md)** - Detailed feature descriptions
- **[Deployment Guide](DEPLOYMENT_GUIDE.md)** - Production deployment instructions
- **[QA Checklist](QA_CHECKLIST.md)** - Quality assurance verification

## 🌐 Arabic Localization

This project is built with Arabic as the primary language:
- **RTL Layout**: Complete right-to-left interface
- **Arabic Fonts**: Cairo and Tajawal fonts for optimal readability
- **Cultural Adaptation**: Saudi Arabia-specific features (VAT, regions, phone formats)
- **Number Formatting**: Proper Arabic number display
- **Date/Time**: Arabic date and time formatting

## 🔒 Security Features

- **Authentication**: Secure JWT-based authentication with Supabase
- **Authorization**: Role-based access control for admin features
- **Data Protection**: Row Level Security policies in database
- **Input Validation**: Comprehensive server-side validation
- **XSS Prevention**: Sanitized user inputs and outputs
- **CSRF Protection**: Built-in protection via Supabase

## 🚀 Performance

- **Fast Loading**: Optimized bundle size and lazy loading
- **Responsive**: Mobile-first design with smooth interactions
- **Caching**: Intelligent data caching with React Query
- **Images**: Optimized image loading and display
- **Code Splitting**: Automatic code splitting for better performance

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 👨‍💻 Author

**Ahmed** - Full Stack Developer
- Specialized in Arabic e-commerce solutions
- Expert in React, Python, and modern web technologies

## 🙏 Acknowledgments

- **Supabase** - For providing excellent backend-as-a-service
- **React Team** - For the amazing React framework
- **Flask Team** - For the lightweight Python web framework
- **Arabic Web Community** - For inspiration and best practices

---

**متجر شغف** - Built with ❤️ for the Arabic e-commerce community
