"""
Main Flask application for the e-commerce backend using local database
"""

import os
from flask import Flask, jsonify
from flask_cors import CORS
from flask_jwt_extended import JWTManager
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def create_app():
    """Create and configure the Flask application"""
    app = Flask(__name__)
    
    # Configuration
    app.config['SECRET_KEY'] = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    app.config['JWT_SECRET_KEY'] = os.getenv('JWT_SECRET_KEY', 'jwt-secret-change-in-production')
    app.config['JWT_ACCESS_TOKEN_EXPIRES'] = False  # Tokens don't expire for development
    app.config['MAX_CONTENT_LENGTH'] = int(os.getenv('MAX_CONTENT_LENGTH', 16 * 1024 * 1024))  # 16MB
    
    # Initialize extensions
    CORS(app, origins=["http://localhost:5173", "http://localhost:3000"])  # Allow frontend origins
    jwt = JWTManager(app)
    
    # Import and register blueprints using local database
    from routes.auth_local import auth_bp
    from routes.products_local import products_bp
    from routes.cart_local import cart_bp
    from routes.categories_local import categories_bp
    
    app.register_blueprint(auth_bp, url_prefix='/api/auth')
    app.register_blueprint(products_bp, url_prefix='/api/products')
    app.register_blueprint(cart_bp, url_prefix='/api/cart')
    app.register_blueprint(categories_bp, url_prefix='/api/categories')
    
    # Simple orders endpoint
    @app.route('/api/orders', methods=['GET'])
    def get_orders():
        return jsonify({
            'success': True,
            'data': [],
            'message': 'لا توجد طلبات بعد',
            'message_en': 'No orders yet'
        })
    
    # Simple admin endpoint
    @app.route('/api/admin/stats', methods=['GET'])
    def admin_stats():
        return jsonify({
            'success': True,
            'data': {
                'total_products': 6,
                'total_categories': 4,
                'total_users': 1,
                'total_orders': 0
            },
            'message': 'إحصائيات المتجر',
            'message_en': 'Store statistics'
        })
    
    # Error handlers
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({'error': 'المورد غير موجود', 'message': 'Resource not found'}), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return jsonify({'error': 'خطأ في الخادم', 'message': 'Internal server error'}), 500
    
    @app.errorhandler(400)
    def bad_request(error):
        return jsonify({'error': 'طلب غير صحيح', 'message': 'Bad request'}), 400
    
    # JWT error handlers
    @jwt.expired_token_loader
    def expired_token_callback(jwt_header, jwt_payload):
        return jsonify({'error': 'انتهت صلاحية الجلسة', 'message': 'Token has expired'}), 401
    
    @jwt.invalid_token_loader
    def invalid_token_callback(error):
        return jsonify({'error': 'رمز غير صحيح', 'message': 'Invalid token'}), 401
    
    @jwt.unauthorized_loader
    def missing_token_callback(error):
        return jsonify({'error': 'مطلوب تسجيل الدخول', 'message': 'Authorization token required'}), 401
    
    # Health check endpoint
    @app.route('/api/health')
    def health_check():
        return jsonify({
            'status': 'healthy',
            'message': 'الخادم يعمل بشكل طبيعي',
            'version': '1.0.0',
            'database': 'SQLite Local'
        })
    
    return app

if __name__ == '__main__':
    app = create_app()
    print("🚀 Starting E-commerce Backend Server...")
    print("🔗 API Health Check: http://localhost:5000/api/health")
    print("📱 Frontend should connect to: http://localhost:5000")
    print("📊 Products API: http://localhost:5000/api/products")
    print("🏷️ Categories API: http://localhost:5000/api/categories")
    
    app.run(
        host='0.0.0.0',
        port=int(os.getenv('PORT', 5000)),
        debug=os.getenv('FLASK_DEBUG', 'True').lower() == 'true'
    )