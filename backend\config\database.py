"""
Database configuration and Supabase client setup
"""

import os
from supabase import create_client, Client
from dotenv import load_dotenv

load_dotenv()

# Supabase configuration
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_KEY = os.getenv('SUPABASE_KEY')
SUPABASE_SERVICE_KEY = os.getenv('SUPABASE_SERVICE_KEY')

# Create Supabase clients
supabase: Client = None
supabase_admin: Client = None

def init_supabase():
    """Initialize Supabase clients"""
    global supabase, supabase_admin
    
    if not SUPABASE_URL or not SUPABASE_KEY:
        raise ValueError("Supabase URL and Key must be provided in environment variables")
    
    # Regular client for user operations
    supabase = create_client(SUPABASE_URL, SUPABASE_KEY)
    
    # Admin client for administrative operations
    if SUPABASE_SERVICE_KEY:
        supabase_admin = create_client(SUPABASE_URL, SUPABASE_SERVICE_KEY)
    else:
        supabase_admin = supabase
    
    return supabase, supabase_admin

def get_supabase_client():
    """Get the regular Supabase client"""
    global supabase
    if supabase is None:
        init_supabase()
    return supabase

def get_supabase_admin_client():
    """Get the admin Supabase client"""
    global supabase_admin
    if supabase_admin is None:
        init_supabase()
    return supabase_admin

# Database table names
TABLES = {
    'users': 'users',
    'categories': 'categories',
    'products': 'products',
    'cart_items': 'cart_items',
    'orders': 'orders',
    'order_items': 'order_items',
    'reviews': 'reviews',
    'admin_users': 'admin_users'
}

# Initialize on import
try:
    init_supabase()
    print("Supabase initialized successfully")
except Exception as e:
    print(f"Warning: Could not initialize Supabase: {e}")
    print("Make sure to set SUPABASE_URL and SUPABASE_KEY in your .env file")
    print("Falling back to local SQLite database...")
    
    # Import and initialize local database as fallback
    from .local_database import init_database
    init_database()
