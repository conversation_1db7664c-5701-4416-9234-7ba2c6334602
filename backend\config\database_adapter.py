"""
Database adapter to work with both Supabase and SQLite
"""

import os
import sqlite3
import json
from contextlib import contextmanager
import bcrypt
from datetime import datetime
import uuid

DATABASE_PATH = os.path.join(os.path.dirname(__file__), '..', 'local_dev.db')

class DatabaseAdapter:
    def __init__(self):
        self.use_supabase = False
        self.supabase = None
        
        # Try to initialize Supabase first
        try:
            from config.database import get_supabase_client
            self.supabase = get_supabase_client()
            self.use_supabase = True
            print("Using Supabase database")
        except:
            print("Using SQLite database")
            self.init_sqlite()
    
    def init_sqlite(self):
        """Initialize SQLite database"""
        if not os.path.exists(DATABASE_PATH):
            self.create_tables()
            self.insert_sample_data()
    
    @contextmanager
    def get_connection(self):
        """Get database connection"""
        conn = sqlite3.connect(DATABASE_PATH)
        conn.row_factory = sqlite3.Row
        try:
            yield conn
        finally:
            conn.close()
    
    def create_tables(self):
        """Create SQLite tables"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Users table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS users (
                    id TEXT PRIMARY KEY,
                    email TEXT UNIQUE NOT NULL,
                    first_name TEXT,
                    last_name TEXT,
                    phone TEXT,
                    password_hash TEXT NOT NULL,
                    is_active INTEGER DEFAULT 1,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Categories table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS categories (
                    id TEXT PRIMARY KEY,
                    name_ar TEXT NOT NULL,
                    name_en TEXT,
                    description_ar TEXT,
                    description_en TEXT,
                    image_url TEXT,
                    is_active INTEGER DEFAULT 1,
                    sort_order INTEGER DEFAULT 0,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Products table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS products (
                    id TEXT PRIMARY KEY,
                    name_ar TEXT NOT NULL,
                    name_en TEXT,
                    description_ar TEXT,
                    description_en TEXT,
                    short_description_ar TEXT,
                    short_description_en TEXT,
                    price REAL NOT NULL,
                    sale_price REAL,
                    sku TEXT UNIQUE,
                    stock_quantity INTEGER DEFAULT 0,
                    category_id TEXT,
                    images TEXT DEFAULT '[]',
                    is_active INTEGER DEFAULT 1,
                    is_featured INTEGER DEFAULT 0,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (category_id) REFERENCES categories (id)
                )
            ''')
            
            # Cart items table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS cart_items (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    product_id TEXT NOT NULL,
                    quantity INTEGER NOT NULL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    FOREIGN KEY (product_id) REFERENCES products (id),
                    UNIQUE(user_id, product_id)
                )
            ''')
            
            # Orders table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS orders (
                    id TEXT PRIMARY KEY,
                    user_id TEXT,
                    order_number TEXT UNIQUE NOT NULL,
                    status TEXT DEFAULT 'pending',
                    payment_status TEXT DEFAULT 'pending',
                    subtotal REAL NOT NULL,
                    tax_amount REAL DEFAULT 0,
                    shipping_amount REAL DEFAULT 0,
                    total_amount REAL NOT NULL,
                    shipping_address TEXT,
                    billing_address TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # Order items table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS order_items (
                    id TEXT PRIMARY KEY,
                    order_id TEXT NOT NULL,
                    product_id TEXT,
                    product_name_ar TEXT NOT NULL,
                    product_name_en TEXT,
                    quantity INTEGER NOT NULL,
                    unit_price REAL NOT NULL,
                    total_price REAL NOT NULL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (order_id) REFERENCES orders (id),
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            ''')
            
            # Reviews table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS reviews (
                    id TEXT PRIMARY KEY,
                    product_id TEXT NOT NULL,
                    user_id TEXT NOT NULL,
                    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
                    title TEXT,
                    comment TEXT,
                    is_approved INTEGER DEFAULT 0,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (product_id) REFERENCES products (id),
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            # Admin users table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS admin_users (
                    id TEXT PRIMARY KEY,
                    user_id TEXT UNIQUE NOT NULL,
                    role TEXT DEFAULT 'admin',
                    is_active INTEGER DEFAULT 1,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            ''')
            
            conn.commit()
    
    def insert_sample_data(self):
        """Insert sample data"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # Sample categories
            categories = [
                (str(uuid.uuid4()), "إلكترونيات", "Electronics", "أجهزة إلكترونية متنوعة", "Various electronic devices", "https://via.placeholder.com/300x300?text=Electronics"),
                (str(uuid.uuid4()), "أزياء", "Fashion", "ملابس وإكسسوارات", "Clothing and accessories", "https://via.placeholder.com/300x300?text=Fashion"),
                (str(uuid.uuid4()), "كتب", "Books", "كتب ومجلات", "Books and magazines", "https://via.placeholder.com/300x300?text=Books"),
                (str(uuid.uuid4()), "منزل وحديقة", "Home & Garden", "أدوات منزلية", "Home utilities", "https://via.placeholder.com/300x300?text=Home")
            ]
            
            cursor.executemany('''
                INSERT OR IGNORE INTO categories (id, name_ar, name_en, description_ar, description_en, image_url)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', categories)
            
            # Get category IDs for products
            cursor.execute("SELECT id FROM categories LIMIT 4")
            category_ids = [row[0] for row in cursor.fetchall()]
            
            # Sample products
            products = [
                (str(uuid.uuid4()), "لابتوب Dell XPS", "Dell XPS Laptop", "لابتوب Dell XPS عالي الأداء مع معالج Intel Core i7", "High performance Dell XPS laptop with Intel Core i7", "لابتوب متطور للمحترفين", "Advanced laptop for professionals", 2999.99, 2699.99, "DELL-XPS001", 10, category_ids[0] if category_ids else None, '["https://via.placeholder.com/400x400?text=Dell+XPS", "https://via.placeholder.com/400x400?text=Dell+XPS+2"]', 1, 1),
                
                (str(uuid.uuid4()), "هاتف iPhone 15", "iPhone 15", "أحدث هاتف من Apple مع ميزات متطورة", "Latest iPhone from Apple with advanced features", "هاتف ذكي متطور", "Advanced smartphone", 3999.99, 3699.99, "IPHONE-15", 15, category_ids[0] if category_ids else None, '["https://via.placeholder.com/400x400?text=iPhone+15", "https://via.placeholder.com/400x400?text=iPhone+15+Pro"]', 1, 1),
                
                (str(uuid.uuid4()), "قميص قطني كلاسيكي", "Classic Cotton Shirt", "قميص قطني عالي الجودة مناسب للعمل", "High quality cotton shirt suitable for work", "قميص أنيق ومريح", "Elegant and comfortable shirt", 159.99, 129.99, "SHIRT-001", 25, category_ids[1] if category_ids else None, '["https://via.placeholder.com/400x400?text=Cotton+Shirt", "https://via.placeholder.com/400x400?text=Shirt+Blue"]', 1, 0),
                
                (str(uuid.uuid4()), "كتاب تعلم Python", "Python Programming Book", "دليل شامل لتعلم البرمجة بلغة Python", "Complete guide to learning Python programming", "للمبتدئين والمتقدمين", "For beginners and advanced", 89.99, 69.99, "BOOK-PY001", 50, category_ids[2] if category_ids else None, '["https://via.placeholder.com/400x400?text=Python+Book", "https://via.placeholder.com/400x400?text=Programming"]', 1, 1),
                
                (str(uuid.uuid4()), "مصباح LED ذكي", "Smart LED Lamp", "مصباح LED قابل للتحكم عبر الهاتف", "LED lamp controllable via smartphone", "إضاءة ذكية موفرة للطاقة", "Smart energy-saving lighting", 199.99, None, "LAMP-LED001", 30, category_ids[3] if category_ids else None, '["https://via.placeholder.com/400x400?text=Smart+LED", "https://via.placeholder.com/400x400?text=LED+Lamp"]', 1, 0),
                
                (str(uuid.uuid4()), "ساعة Apple Watch", "Apple Watch Series 9", "ساعة ذكية من Apple بميزات متقدمة", "Smart watch from Apple with advanced features", "تتبع اللياقة والصحة", "Fitness and health tracking", 1599.99, 1399.99, "WATCH-AW9", 8, category_ids[0] if category_ids else None, '["https://via.placeholder.com/400x400?text=Apple+Watch", "https://via.placeholder.com/400x400?text=Smart+Watch"]', 1, 1)
            ]
            
            cursor.executemany('''
                INSERT OR IGNORE INTO products (id, name_ar, name_en, description_ar, description_en, short_description_ar, short_description_en, price, sale_price, sku, stock_quantity, category_id, images, is_active, is_featured)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', products)
            
            # Create admin user
            admin_id = str(uuid.uuid4())
            admin_password = bcrypt.hashpw("admin123".encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            cursor.execute('''
                INSERT OR IGNORE INTO users (id, email, first_name, last_name, password_hash)
                VALUES (?, ?, ?, ?, ?)
            ''', (admin_id, "<EMAIL>", "المدير", "العام", admin_password))
            
            cursor.execute('''
                INSERT OR IGNORE INTO admin_users (id, user_id, role)
                VALUES (?, ?, ?)
            ''', (str(uuid.uuid4()), admin_id, "super_admin"))
            
            conn.commit()
            print("Sample data inserted successfully")
    
    # Generic query methods
    def select(self, table, columns="*", where=None, params=None, limit=None, offset=None, order_by=None):
        """Generic SELECT query"""
        if self.use_supabase and self.supabase:
            # Supabase implementation would go here
            pass
        else:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                query = f"SELECT {columns} FROM {table}"
                query_params = []
                
                if where:
                    query += f" WHERE {where}"
                    if params:
                        query_params.extend(params)
                
                if order_by:
                    query += f" ORDER BY {order_by}"
                
                if limit:
                    query += f" LIMIT {limit}"
                    if offset:
                        query += f" OFFSET {offset}"
                
                cursor.execute(query, query_params)
                return cursor.fetchall()
    
    def insert(self, table, data):
        """Generic INSERT query"""
        if self.use_supabase and self.supabase:
            # Supabase implementation would go here
            pass
        else:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # Add ID if not present
                if 'id' not in data:
                    data['id'] = str(uuid.uuid4())
                
                columns = ', '.join(data.keys())
                placeholders = ', '.join(['?' for _ in data])
                values = list(data.values())
                
                query = f"INSERT INTO {table} ({columns}) VALUES ({placeholders})"
                cursor.execute(query, values)
                conn.commit()
                return data['id']
    
    def update(self, table, data, where, params=None):
        """Generic UPDATE query"""
        if self.use_supabase and self.supabase:
            # Supabase implementation would go here
            pass
        else:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                set_clause = ', '.join([f"{key} = ?" for key in data.keys()])
                values = list(data.values())
                
                if params:
                    values.extend(params)
                
                query = f"UPDATE {table} SET {set_clause} WHERE {where}"
                cursor.execute(query, values)
                conn.commit()
                return cursor.rowcount
    
    def delete(self, table, where, params=None):
        """Generic DELETE query"""
        if self.use_supabase and self.supabase:
            # Supabase implementation would go here
            pass
        else:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                query = f"DELETE FROM {table} WHERE {where}"
                cursor.execute(query, params or [])
                conn.commit()
                return cursor.rowcount

# Global database adapter instance
db = DatabaseAdapter()