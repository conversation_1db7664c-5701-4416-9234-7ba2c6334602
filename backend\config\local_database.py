"""
Local SQLite database configuration for development
"""

import sqlite3
import os
from contextlib import contextmanager

DATABASE_PATH = os.path.join(os.path.dirname(__file__), '..', 'local_dev.db')

def init_database():
    """Initialize the local SQLite database with basic schema"""
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()
    
    # Create users table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            email TEXT UNIQUE NOT NULL,
            first_name TEXT,
            last_name TEXT,
            phone TEXT,
            password_hash TEXT NOT NULL,
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create categories table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name_ar TEXT NOT NULL,
            name_en TEXT,
            description_ar TEXT,
            description_en TEXT,
            image_url TEXT,
            is_active BOOLEAN DEFAULT 1,
            sort_order INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create products table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name_ar TEXT NOT NULL,
            name_en TEXT,
            description_ar TEXT,
            description_en TEXT,
            price REAL NOT NULL,
            sale_price REAL,
            sku TEXT UNIQUE,
            stock_quantity INTEGER DEFAULT 0,
            category_id INTEGER,
            images TEXT DEFAULT '[]',
            is_active BOOLEAN DEFAULT 1,
            is_featured BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories (id)
        )
    ''')
    
    # Create cart_items table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS cart_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            product_id INTEGER NOT NULL,
            quantity INTEGER NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id),
            FOREIGN KEY (product_id) REFERENCES products (id),
            UNIQUE(user_id, product_id)
        )
    ''')
    
    # Create orders table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS orders (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER,
            order_number TEXT UNIQUE NOT NULL,
            status TEXT DEFAULT 'pending',
            payment_status TEXT DEFAULT 'pending',
            subtotal REAL NOT NULL,
            tax_amount REAL DEFAULT 0,
            shipping_amount REAL DEFAULT 0,
            total_amount REAL NOT NULL,
            shipping_address TEXT,
            billing_address TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    # Create order_items table
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS order_items (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            order_id INTEGER NOT NULL,
            product_id INTEGER,
            product_name_ar TEXT NOT NULL,
            product_name_en TEXT,
            quantity INTEGER NOT NULL,
            unit_price REAL NOT NULL,
            total_price REAL NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (order_id) REFERENCES orders (id),
            FOREIGN KEY (product_id) REFERENCES products (id)
        )
    ''')
    
    # Insert sample data
    insert_sample_data(cursor)
    
    conn.commit()
    conn.close()
    print(f"Database initialized at: {DATABASE_PATH}")

def insert_sample_data(cursor):
    """Insert sample data for testing"""
    
    # Sample categories
    categories = [
        ("إلكترونيات", "Electronics", "أجهزة إلكترونية متنوعة", "Various electronic devices", "https://via.placeholder.com/200x200?text=Electronics"),
        ("أزياء", "Fashion", "ملابس وإكسسوارات", "Clothing and accessories", "https://via.placeholder.com/200x200?text=Fashion"),
        ("كتب", "Books", "كتب ومجلات", "Books and magazines", "https://via.placeholder.com/200x200?text=Books"),
        ("منزل وحديقة", "Home & Garden", "أدوات منزلية", "Home utilities", "https://via.placeholder.com/200x200?text=Home")
    ]
    
    cursor.executemany('''
        INSERT OR IGNORE INTO categories (name_ar, name_en, description_ar, description_en, image_url)
        VALUES (?, ?, ?, ?, ?)
    ''', categories)
    
    # Sample products
    products = [
        ("لابتوب Dell", "Dell Laptop", "لابتوب Dell عالي الأداء", "High performance Dell laptop", 2999.99, None, "DELL001", 10, 1, '["https://via.placeholder.com/400x400?text=Dell+Laptop"]', 1, 1),
        ("هاتف ذكي", "Smartphone", "هاتف ذكي حديث", "Modern smartphone", 1599.99, 1299.99, "PHONE001", 15, 1, '["https://via.placeholder.com/400x400?text=Smartphone"]', 1, 1),
        ("قميص قطني", "Cotton Shirt", "قميص قطني مريح", "Comfortable cotton shirt", 89.99, None, "SHIRT001", 25, 2, '["https://via.placeholder.com/400x400?text=Cotton+Shirt"]', 1, 0),
        ("كتاب البرمجة", "Programming Book", "كتاب تعلم البرمجة", "Learn programming book", 49.99, 39.99, "BOOK001", 50, 3, '["https://via.placeholder.com/400x400?text=Programming+Book"]', 1, 1),
        ("مصباح LED", "LED Lamp", "مصباح LED موفر للطاقة", "Energy efficient LED lamp", 79.99, None, "LAMP001", 30, 4, '["https://via.placeholder.com/400x400?text=LED+Lamp"]', 1, 0),
        ("ساعة ذكية", "Smart Watch", "ساعة ذكية متقدمة", "Advanced smart watch", 599.99, 499.99, "WATCH001", 8, 1, '["https://via.placeholder.com/400x400?text=Smart+Watch"]', 1, 1)
    ]
    
    cursor.executemany('''
        INSERT OR IGNORE INTO products (name_ar, name_en, description_ar, description_en, price, sale_price, sku, stock_quantity, category_id, images, is_active, is_featured)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', products)

@contextmanager
def get_db_connection():
    """Get database connection context manager"""
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row  # This enables column access by name
    try:
        yield conn
    finally:
        conn.close()

def get_db_cursor():
    """Get database cursor (for backward compatibility)"""
    conn = sqlite3.connect(DATABASE_PATH)
    conn.row_factory = sqlite3.Row
    return conn, conn.cursor()

# Initialize database on import
if not os.path.exists(DATABASE_PATH):
    init_database()