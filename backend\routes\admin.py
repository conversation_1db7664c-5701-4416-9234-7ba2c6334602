"""
Admin routes for managing the e-commerce platform
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from config.database import get_supabase_client, TABLES
from utils.helpers import generate_response, is_valid_uuid, paginate_query
from utils.validators import validate_product_data, validate_email

admin_bp = Blueprint('admin', __name__)

def verify_admin():
    """Verify if current user is admin"""
    user_id = get_jwt_identity()
    supabase = get_supabase_client()
    
    admin_response = supabase.table(TABLES['admin_users']).select('id').eq('user_id', user_id).eq('is_active', True).execute()
    return bool(admin_response.data)

@admin_bp.route('/dashboard', methods=['GET'])
@jwt_required()
def get_dashboard_stats():
    """Get admin dashboard statistics"""
    try:
        if not verify_admin():
            return generate_response(
                success=False,
                message='غير مصرح لك بالوصول',
                message_en='Access denied'
            ), 403
        
        supabase = get_supabase_client()
        
        # Get total counts
        users_count = supabase.table(TABLES['users']).select('id', count='exact').execute().count or 0
        products_count = supabase.table(TABLES['products']).select('id', count='exact').eq('is_active', True).execute().count or 0
        orders_count = supabase.table(TABLES['orders']).select('id', count='exact').execute().count or 0
        categories_count = supabase.table(TABLES['categories']).select('id', count='exact').eq('is_active', True).execute().count or 0
        
        # Get recent orders
        recent_orders = supabase.table(TABLES['orders']).select(
            'id, order_number, status, total_amount, created_at, users(first_name, last_name)'
        ).order('created_at', desc=True).limit(5).execute().data
        
        # Get low stock products
        low_stock_products = supabase.table(TABLES['products']).select(
            'id, name_ar, name_en, stock_quantity'
        ).eq('is_active', True).lte('stock_quantity', 10).order('stock_quantity').limit(10).execute().data
        
        # Calculate total revenue
        revenue_response = supabase.table(TABLES['orders']).select('total_amount').eq('payment_status', 'paid').execute()
        total_revenue = sum(order['total_amount'] for order in revenue_response.data) if revenue_response.data else 0
        
        return generate_response(
            success=True,
            message='تم جلب إحصائيات لوحة التحكم بنجاح',
            message_en='Dashboard statistics retrieved successfully',
            data={
                'stats': {
                    'users_count': users_count,
                    'products_count': products_count,
                    'orders_count': orders_count,
                    'categories_count': categories_count,
                    'total_revenue': total_revenue
                },
                'recent_orders': recent_orders,
                'low_stock_products': low_stock_products
            }
        )
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في جلب إحصائيات لوحة التحكم',
            message_en='Error retrieving dashboard statistics',
            error=str(e)
        ), 500

@admin_bp.route('/products', methods=['GET'])
@jwt_required()
def get_admin_products():
    """Get all products for admin management"""
    try:
        if not verify_admin():
            return generate_response(
                success=False,
                message='غير مصرح لك بالوصول',
                message_en='Access denied'
            ), 403
        
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 100)
        search = request.args.get('search', '').strip()
        category_id = request.args.get('category_id')
        status = request.args.get('status')  # active, inactive, all
        
        supabase = get_supabase_client()
        
        query = supabase.table(TABLES['products']).select(
            'id, name_ar, name_en, price, sale_price, stock_quantity, is_active, is_featured, '
            'categories(name_ar, name_en), created_at'
        )
        
        # Apply filters
        if status == 'active':
            query = query.eq('is_active', True)
        elif status == 'inactive':
            query = query.eq('is_active', False)
        
        if category_id and is_valid_uuid(category_id):
            query = query.eq('category_id', category_id)
        
        if search:
            query = query.or_(f'name_ar.ilike.%{search}%,name_en.ilike.%{search}%')
        
        # Get total count
        count_query = supabase.table(TABLES['products']).select('id', count='exact')
        if status == 'active':
            count_query = count_query.eq('is_active', True)
        elif status == 'inactive':
            count_query = count_query.eq('is_active', False)
        if category_id and is_valid_uuid(category_id):
            count_query = count_query.eq('category_id', category_id)
        if search:
            count_query = count_query.or_(f'name_ar.ilike.%{search}%,name_en.ilike.%{search}%')
        
        total_count = count_query.execute().count or 0
        
        # Apply pagination and execute
        query = paginate_query(query.order('created_at', desc=True), page, per_page)
        response = query.execute()
        
        total_pages = (total_count + per_page - 1) // per_page
        
        return generate_response(
            success=True,
            message='تم جلب المنتجات بنجاح',
            message_en='Products retrieved successfully',
            data={
                'products': response.data,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total_count,
                    'total_pages': total_pages,
                    'has_next': page < total_pages,
                    'has_prev': page > 1
                }
            }
        )
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في جلب المنتجات',
            message_en='Error retrieving products',
            error=str(e)
        ), 500

@admin_bp.route('/products', methods=['POST'])
@jwt_required()
def create_product():
    """Create new product"""
    try:
        if not verify_admin():
            return generate_response(
                success=False,
                message='غير مصرح لك بالوصول',
                message_en='Access denied'
            ), 403
        
        data = request.get_json()
        
        # Validate product data
        is_valid, error_msg = validate_product_data(data)
        if not is_valid:
            return generate_response(
                success=False,
                message=error_msg,
                message_en='Invalid product data'
            ), 400
        
        supabase = get_supabase_client()
        
        # Create product
        product_data = {
            'name_ar': data['name_ar'],
            'name_en': data.get('name_en', ''),
            'description_ar': data.get('description_ar', ''),
            'description_en': data.get('description_en', ''),
            'short_description_ar': data.get('short_description_ar', ''),
            'short_description_en': data.get('short_description_en', ''),
            'price': float(data['price']),
            'sale_price': float(data['sale_price']) if data.get('sale_price') else None,
            'category_id': data['category_id'],
            'sku': data.get('sku', ''),
            'stock_quantity': int(data.get('stock_quantity', 0)),
            'weight': float(data.get('weight', 0)) if data.get('weight') else None,
            'dimensions': data.get('dimensions'),
            'images': data.get('images', []),
            'tags': data.get('tags', []),
            'is_featured': data.get('is_featured', False),
            'is_active': data.get('is_active', True),
            'meta_title_ar': data.get('meta_title_ar', ''),
            'meta_title_en': data.get('meta_title_en', ''),
            'meta_description_ar': data.get('meta_description_ar', ''),
            'meta_description_en': data.get('meta_description_en', '')
        }
        
        response = supabase.table(TABLES['products']).insert(product_data).execute()
        
        if response.data:
            return generate_response(
                success=True,
                message='تم إنشاء المنتج بنجاح',
                message_en='Product created successfully',
                data={'product': response.data[0]}
            ), 201
        
        return generate_response(
            success=False,
            message='فشل في إنشاء المنتج',
            message_en='Failed to create product'
        ), 500
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في إنشاء المنتج',
            message_en='Error creating product',
            error=str(e)
        ), 500

@admin_bp.route('/orders', methods=['GET'])
@jwt_required()
def get_admin_orders():
    """Get all orders for admin management"""
    try:
        if not verify_admin():
            return generate_response(
                success=False,
                message='غير مصرح لك بالوصول',
                message_en='Access denied'
            ), 403
        
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 100)
        status = request.args.get('status')
        
        supabase = get_supabase_client()
        
        query = supabase.table(TABLES['orders']).select(
            'id, order_number, status, payment_status, total_amount, created_at, '
            'users(first_name, last_name, email)'
        )
        
        if status:
            query = query.eq('status', status)
        
        # Get total count
        count_query = supabase.table(TABLES['orders']).select('id', count='exact')
        if status:
            count_query = count_query.eq('status', status)
        
        total_count = count_query.execute().count or 0
        
        # Apply pagination and execute
        query = paginate_query(query.order('created_at', desc=True), page, per_page)
        response = query.execute()
        
        total_pages = (total_count + per_page - 1) // per_page
        
        return generate_response(
            success=True,
            message='تم جلب الطلبات بنجاح',
            message_en='Orders retrieved successfully',
            data={
                'orders': response.data,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total_count,
                    'total_pages': total_pages,
                    'has_next': page < total_pages,
                    'has_prev': page > 1
                }
            }
        )
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في جلب الطلبات',
            message_en='Error retrieving orders',
            error=str(e)
        ), 500

@admin_bp.route('/orders/<order_id>/status', methods=['PUT'])
@jwt_required()
def update_order_status(order_id):
    """Update order status"""
    try:
        if not verify_admin():
            return generate_response(
                success=False,
                message='غير مصرح لك بالوصول',
                message_en='Access denied'
            ), 403
        
        if not is_valid_uuid(order_id):
            return generate_response(
                success=False,
                message='معرف الطلب غير صحيح',
                message_en='Invalid order ID'
            ), 400
        
        data = request.get_json()
        new_status = data.get('status')
        
        if not new_status or not validate_order_status(new_status):
            return generate_response(
                success=False,
                message='حالة الطلب غير صحيحة',
                message_en='Invalid order status'
            ), 400
        
        supabase = get_supabase_client()
        
        update_data = {'status': new_status}
        
        # Add timestamps for specific statuses
        if new_status == 'shipped':
            update_data['shipped_at'] = 'now()'
        elif new_status == 'delivered':
            update_data['delivered_at'] = 'now()'
        
        response = supabase.table(TABLES['orders']).update(update_data).eq('id', order_id).execute()
        
        if response.data:
            return generate_response(
                success=True,
                message='تم تحديث حالة الطلب بنجاح',
                message_en='Order status updated successfully',
                data={'order': response.data[0]}
            )
        
        return generate_response(
            success=False,
            message='فشل في تحديث حالة الطلب',
            message_en='Failed to update order status'
        ), 500
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في تحديث حالة الطلب',
            message_en='Error updating order status',
            error=str(e)
        ), 500
