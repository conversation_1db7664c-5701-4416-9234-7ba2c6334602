"""
Authentication routes for user registration, login, and profile management
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity
import bcrypt
from config.database import get_supabase_client, TABLES
from utils.validators import validate_email, validate_password
from utils.helpers import generate_response

auth_bp = Blueprint('auth', __name__)

@auth_bp.route('/register', methods=['POST'])
def register():
    """Register a new user"""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['email', 'password', 'first_name', 'last_name']
        for field in required_fields:
            if not data.get(field):
                return generate_response(
                    success=False,
                    message=f'حقل {field} مطلوب',
                    message_en=f'{field} is required'
                ), 400
        
        email = data['email'].lower().strip()
        password = data['password']
        first_name = data['first_name'].strip()
        last_name = data['last_name'].strip()
        phone = data.get('phone', '').strip()
        
        # Validate email and password
        if not validate_email(email):
            return generate_response(
                success=False,
                message='البريد الإلكتروني غير صحيح',
                message_en='Invalid email format'
            ), 400
        
        if not validate_password(password):
            return generate_response(
                success=False,
                message='كلمة المرور يجب أن تكون 8 أحرف على الأقل',
                message_en='Password must be at least 8 characters'
            ), 400
        
        supabase = get_supabase_client()
        
        # Check if user already exists
        existing_user = supabase.table(TABLES['users']).select('id').eq('email', email).execute()
        if existing_user.data:
            return generate_response(
                success=False,
                message='البريد الإلكتروني مستخدم بالفعل',
                message_en='Email already exists'
            ), 400
        
        # Create user with Supabase Auth
        auth_response = supabase.auth.sign_up({
            'email': email,
            'password': password
        })
        
        if auth_response.user:
            # Create user profile
            user_data = {
                'id': auth_response.user.id,
                'email': email,
                'first_name': first_name,
                'last_name': last_name,
                'phone': phone,
                'is_active': True,
                'email_verified': False
            }
            
            profile_response = supabase.table(TABLES['users']).insert(user_data).execute()
            
            if profile_response.data:
                # Create access token
                access_token = create_access_token(identity=auth_response.user.id)
                
                return generate_response(
                    success=True,
                    message='تم إنشاء الحساب بنجاح',
                    message_en='Account created successfully',
                    data={
                        'user': profile_response.data[0],
                        'access_token': access_token
                    }
                ), 201
        
        return generate_response(
            success=False,
            message='فشل في إنشاء الحساب',
            message_en='Failed to create account'
        ), 500
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في الخادم',
            message_en='Server error',
            error=str(e)
        ), 500

@auth_bp.route('/login', methods=['POST'])
def login():
    """Login user"""
    try:
        data = request.get_json()
        
        email = data.get('email', '').lower().strip()
        password = data.get('password', '')
        
        if not email or not password:
            return generate_response(
                success=False,
                message='البريد الإلكتروني وكلمة المرور مطلوبان',
                message_en='Email and password are required'
            ), 400
        
        supabase = get_supabase_client()
        
        # Authenticate with Supabase
        auth_response = supabase.auth.sign_in_with_password({
            'email': email,
            'password': password
        })
        
        if auth_response.user:
            # Get user profile
            user_response = supabase.table(TABLES['users']).select('*').eq('id', auth_response.user.id).execute()
            
            if user_response.data and user_response.data[0]['is_active']:
                user = user_response.data[0]
                
                # Create access token
                access_token = create_access_token(identity=auth_response.user.id)
                
                return generate_response(
                    success=True,
                    message='تم تسجيل الدخول بنجاح',
                    message_en='Login successful',
                    data={
                        'user': user,
                        'access_token': access_token
                    }
                )
            else:
                return generate_response(
                    success=False,
                    message='الحساب غير نشط',
                    message_en='Account is not active'
                ), 403
        
        return generate_response(
            success=False,
            message='البريد الإلكتروني أو كلمة المرور غير صحيحة',
            message_en='Invalid email or password'
        ), 401
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في تسجيل الدخول',
            message_en='Login error',
            error=str(e)
        ), 500

@auth_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_profile():
    """Get user profile"""
    try:
        user_id = get_jwt_identity()
        supabase = get_supabase_client()
        
        user_response = supabase.table(TABLES['users']).select('*').eq('id', user_id).execute()
        
        if user_response.data:
            return generate_response(
                success=True,
                message='تم جلب الملف الشخصي بنجاح',
                message_en='Profile retrieved successfully',
                data={'user': user_response.data[0]}
            )
        
        return generate_response(
            success=False,
            message='المستخدم غير موجود',
            message_en='User not found'
        ), 404
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في جلب الملف الشخصي',
            message_en='Error retrieving profile',
            error=str(e)
        ), 500

@auth_bp.route('/profile', methods=['PUT'])
@jwt_required()
def update_profile():
    """Update user profile"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        # Fields that can be updated
        allowed_fields = ['first_name', 'last_name', 'phone', 'date_of_birth', 'gender']
        update_data = {}
        
        for field in allowed_fields:
            if field in data:
                update_data[field] = data[field]
        
        if not update_data:
            return generate_response(
                success=False,
                message='لا توجد بيانات للتحديث',
                message_en='No data to update'
            ), 400
        
        supabase = get_supabase_client()
        
        response = supabase.table(TABLES['users']).update(update_data).eq('id', user_id).execute()
        
        if response.data:
            return generate_response(
                success=True,
                message='تم تحديث الملف الشخصي بنجاح',
                message_en='Profile updated successfully',
                data={'user': response.data[0]}
            )
        
        return generate_response(
            success=False,
            message='فشل في تحديث الملف الشخصي',
            message_en='Failed to update profile'
        ), 500
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في تحديث الملف الشخصي',
            message_en='Error updating profile',
            error=str(e)
        ), 500

@auth_bp.route('/logout', methods=['POST'])
@jwt_required()
def logout():
    """Logout user"""
    try:
        supabase = get_supabase_client()
        supabase.auth.sign_out()
        
        return generate_response(
            success=True,
            message='تم تسجيل الخروج بنجاح',
            message_en='Logout successful'
        )
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في تسجيل الخروج',
            message_en='Logout error',
            error=str(e)
        ), 500
