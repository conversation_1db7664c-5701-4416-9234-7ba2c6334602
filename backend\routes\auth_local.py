"""
Authentication routes using local database adapter
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity
import bcrypt
import re
from config.database_adapter import db
import uuid

auth_bp = Blueprint('auth', __name__)

def validate_email(email):
    """Validate email format"""
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_password(password):
    """Validate password strength"""
    if len(password) < 6:
        return False, "كلمة المرور يجب أن تكون على الأقل 6 أحرف"
    return True, ""

@auth_bp.route('/register', methods=['POST'])
def register():
    """Register a new user"""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['email', 'password', 'first_name', 'last_name']
        for field in required_fields:
            if not data.get(field):
                return jsonify({
                    'success': False,
                    'error': f'حقل {field} مطلوب',
                    'message': f'{field} is required'
                }), 400
        
        email = data['email'].lower().strip()
        password = data['password']
        first_name = data['first_name'].strip()
        last_name = data['last_name'].strip()
        phone = data.get('phone', '').strip()
        
        # Validate email
        if not validate_email(email):
            return jsonify({
                'success': False,
                'error': 'صيغة البريد الإلكتروني غير صحيحة',
                'message': 'Invalid email format'
            }), 400
        
        # Validate password
        is_valid, password_error = validate_password(password)
        if not is_valid:
            return jsonify({
                'success': False,
                'error': password_error,
                'message': 'Password validation failed'
            }), 400
        
        # Check if user already exists
        existing_users = db.select('users', where='email = ?', params=[email])
        if existing_users:
            return jsonify({
                'success': False,
                'error': 'البريد الإلكتروني مستخدم بالفعل',
                'message': 'Email already exists'
            }), 409
        
        # Hash password
        password_hash = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
        
        # Create user
        user_data = {
            'id': str(uuid.uuid4()),
            'email': email,
            'first_name': first_name,
            'last_name': last_name,
            'phone': phone,
            'password_hash': password_hash,
            'is_active': 1
        }
        
        user_id = db.insert('users', user_data)
        
        # Create access token
        access_token = create_access_token(identity=user_id)
        
        return jsonify({
            'success': True,
            'data': {
                'user': {
                    'id': user_id,
                    'email': email,
                    'first_name': first_name,
                    'last_name': last_name,
                    'phone': phone
                },
                'access_token': access_token
            },
            'message': 'تم إنشاء الحساب بنجاح',
            'message_en': 'Account created successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'خطأ في إنشاء الحساب',
            'message': str(e)
        }), 500

@auth_bp.route('/login', methods=['POST'])
def login():
    """Login user"""
    try:
        data = request.get_json()
        
        # Validate required fields
        if not data.get('email') or not data.get('password'):
            return jsonify({
                'success': False,
                'error': 'البريد الإلكتروني وكلمة المرور مطلوبان',
                'message': 'Email and password are required'
            }), 400
        
        email = data['email'].lower().strip()
        password = data['password']
        
        # Find user
        users = db.select('users', where='email = ? AND is_active = 1', params=[email])
        if not users:
            return jsonify({
                'success': False,
                'error': 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
                'message': 'Invalid email or password'
            }), 401
        
        user = dict(users[0])
        
        # Check password
        if not bcrypt.checkpw(password.encode('utf-8'), user['password_hash'].encode('utf-8')):
            return jsonify({
                'success': False,
                'error': 'البريد الإلكتروني أو كلمة المرور غير صحيحة',
                'message': 'Invalid email or password'
            }), 401
        
        # Create access token
        access_token = create_access_token(identity=user['id'])
        
        # Remove password hash from response
        user_response = {
            'id': user['id'],
            'email': user['email'],
            'first_name': user['first_name'],
            'last_name': user['last_name'],
            'phone': user['phone']
        }
        
        return jsonify({
            'success': True,
            'data': {
                'user': user_response,
                'access_token': access_token
            },
            'message': 'تم تسجيل الدخول بنجاح',
            'message_en': 'Login successful'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'خطأ في تسجيل الدخول',
            'message': str(e)
        }), 500

@auth_bp.route('/profile', methods=['GET'])
@jwt_required()
def get_profile():
    """Get user profile"""
    try:
        user_id = get_jwt_identity()
        
        users = db.select('users', where='id = ? AND is_active = 1', params=[user_id])
        if not users:
            return jsonify({
                'success': False,
                'error': 'المستخدم غير موجود',
                'message': 'User not found'
            }), 404
        
        user = dict(users[0])
        
        # Remove password hash from response
        user_response = {
            'id': user['id'],
            'email': user['email'],
            'first_name': user['first_name'],
            'last_name': user['last_name'],
            'phone': user['phone'],
            'created_at': user['created_at']
        }
        
        return jsonify({
            'success': True,
            'data': user_response,
            'message': 'تم جلب الملف الشخصي بنجاح',
            'message_en': 'Profile retrieved successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'خطأ في جلب الملف الشخصي',
            'message': str(e)
        }), 500

@auth_bp.route('/profile', methods=['PUT'])
@jwt_required()
def update_profile():
    """Update user profile"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        # Check if user exists
        users = db.select('users', where='id = ? AND is_active = 1', params=[user_id])
        if not users:
            return jsonify({
                'success': False,
                'error': 'المستخدم غير موجود',
                'message': 'User not found'
            }), 404
        
        # Prepare update data
        update_data = {}
        allowed_fields = ['first_name', 'last_name', 'phone']
        
        for field in allowed_fields:
            if field in data:
                update_data[field] = data[field].strip() if isinstance(data[field], str) else data[field]
        
        if not update_data:
            return jsonify({
                'success': False,
                'error': 'لا توجد بيانات للتحديث',
                'message': 'No data to update'
            }), 400
        
        # Update user
        db.update('users', update_data, 'id = ?', [user_id])
        
        # Get updated user data
        users = db.select('users', where='id = ?', params=[user_id])
        user = dict(users[0])
        
        user_response = {
            'id': user['id'],
            'email': user['email'],
            'first_name': user['first_name'],
            'last_name': user['last_name'],
            'phone': user['phone'],
            'created_at': user['created_at']
        }
        
        return jsonify({
            'success': True,
            'data': user_response,
            'message': 'تم تحديث الملف الشخصي بنجاح',
            'message_en': 'Profile updated successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'خطأ في تحديث الملف الشخصي',
            'message': str(e)
        }), 500