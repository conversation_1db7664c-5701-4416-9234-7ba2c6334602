"""
Shopping cart routes for managing user cart items
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from config.database import get_supabase_client, TABLES
from utils.helpers import generate_response, is_valid_uuid, calculate_tax, calculate_shipping_cost
from utils.validators import validate_quantity

cart_bp = Blueprint('cart', __name__)

@cart_bp.route('/', methods=['GET'])
@jwt_required()
def get_cart():
    """Get user's cart items"""
    try:
        user_id = get_jwt_identity()
        supabase = get_supabase_client()
        
        # Get cart items with product details
        response = supabase.table(TABLES['cart_items']).select(
            'id, quantity, created_at, '
            'products(id, name_ar, name_en, price, sale_price, images, stock_quantity, weight)'
        ).eq('user_id', user_id).execute()
        
        cart_items = response.data
        
        # Calculate totals
        subtotal = 0
        total_weight = 0
        
        for item in cart_items:
            if item['products']:
                product = item['products']
                # Use sale price if available, otherwise regular price
                price = product['sale_price'] if product['sale_price'] else product['price']
                item['unit_price'] = price
                item['total_price'] = price * item['quantity']
                subtotal += item['total_price']
                
                # Calculate weight
                if product.get('weight'):
                    total_weight += product['weight'] * item['quantity']
        
        # Calculate tax and shipping
        tax_amount = calculate_tax(subtotal)
        shipping_cost = calculate_shipping_cost(subtotal, total_weight)
        total_amount = subtotal + tax_amount + shipping_cost
        
        return generate_response(
            success=True,
            message='تم جلب السلة بنجاح',
            message_en='Cart retrieved successfully',
            data={
                'cart_items': cart_items,
                'summary': {
                    'subtotal': round(subtotal, 2),
                    'tax_amount': tax_amount,
                    'shipping_cost': shipping_cost,
                    'total_amount': round(total_amount, 2),
                    'item_count': len(cart_items),
                    'total_weight': total_weight
                }
            }
        )
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في جلب السلة',
            message_en='Error retrieving cart',
            error=str(e)
        ), 500

@cart_bp.route('/add', methods=['POST'])
@jwt_required()
def add_to_cart():
    """Add item to cart"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        product_id = data.get('product_id')
        quantity = data.get('quantity', 1)
        
        # Validate input
        if not product_id or not is_valid_uuid(product_id):
            return generate_response(
                success=False,
                message='معرف المنتج غير صحيح',
                message_en='Invalid product ID'
            ), 400
        
        if not validate_quantity(quantity):
            return generate_response(
                success=False,
                message='الكمية غير صحيحة',
                message_en='Invalid quantity'
            ), 400
        
        supabase = get_supabase_client()
        
        # Check if product exists and is active
        product_response = supabase.table(TABLES['products']).select(
            'id, name_ar, stock_quantity, is_active'
        ).eq('id', product_id).execute()
        
        if not product_response.data or not product_response.data[0]['is_active']:
            return generate_response(
                success=False,
                message='المنتج غير موجود أو غير متاح',
                message_en='Product not found or not available'
            ), 404
        
        product = product_response.data[0]
        
        # Check stock availability
        if product['stock_quantity'] < quantity:
            return generate_response(
                success=False,
                message=f'الكمية المتاحة فقط {product["stock_quantity"]}',
                message_en=f'Only {product["stock_quantity"]} items available'
            ), 400
        
        # Check if item already exists in cart
        existing_item_response = supabase.table(TABLES['cart_items']).select(
            'id, quantity'
        ).eq('user_id', user_id).eq('product_id', product_id).execute()
        
        if existing_item_response.data:
            # Update existing item
            existing_item = existing_item_response.data[0]
            new_quantity = existing_item['quantity'] + quantity
            
            # Check stock for new quantity
            if product['stock_quantity'] < new_quantity:
                return generate_response(
                    success=False,
                    message=f'الكمية المتاحة فقط {product["stock_quantity"]}',
                    message_en=f'Only {product["stock_quantity"]} items available'
                ), 400
            
            update_response = supabase.table(TABLES['cart_items']).update({
                'quantity': new_quantity
            }).eq('id', existing_item['id']).execute()
            
            if update_response.data:
                return generate_response(
                    success=True,
                    message='تم تحديث السلة بنجاح',
                    message_en='Cart updated successfully',
                    data={'cart_item': update_response.data[0]}
                )
        else:
            # Add new item
            insert_response = supabase.table(TABLES['cart_items']).insert({
                'user_id': user_id,
                'product_id': product_id,
                'quantity': quantity
            }).execute()
            
            if insert_response.data:
                return generate_response(
                    success=True,
                    message='تم إضافة المنتج للسلة بنجاح',
                    message_en='Product added to cart successfully',
                    data={'cart_item': insert_response.data[0]}
                )
        
        return generate_response(
            success=False,
            message='فشل في إضافة المنتج للسلة',
            message_en='Failed to add product to cart'
        ), 500
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في إضافة المنتج للسلة',
            message_en='Error adding product to cart',
            error=str(e)
        ), 500

@cart_bp.route('/update/<cart_item_id>', methods=['PUT'])
@jwt_required()
def update_cart_item(cart_item_id):
    """Update cart item quantity"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        if not is_valid_uuid(cart_item_id):
            return generate_response(
                success=False,
                message='معرف العنصر غير صحيح',
                message_en='Invalid cart item ID'
            ), 400
        
        quantity = data.get('quantity')
        if not validate_quantity(quantity):
            return generate_response(
                success=False,
                message='الكمية غير صحيحة',
                message_en='Invalid quantity'
            ), 400
        
        supabase = get_supabase_client()
        
        # Get cart item and verify ownership
        cart_item_response = supabase.table(TABLES['cart_items']).select(
            'id, product_id, quantity'
        ).eq('id', cart_item_id).eq('user_id', user_id).execute()
        
        if not cart_item_response.data:
            return generate_response(
                success=False,
                message='عنصر السلة غير موجود',
                message_en='Cart item not found'
            ), 404
        
        cart_item = cart_item_response.data[0]
        
        # Check product stock
        product_response = supabase.table(TABLES['products']).select(
            'stock_quantity, is_active'
        ).eq('id', cart_item['product_id']).execute()
        
        if not product_response.data or not product_response.data[0]['is_active']:
            return generate_response(
                success=False,
                message='المنتج غير متاح',
                message_en='Product not available'
            ), 400
        
        product = product_response.data[0]
        
        if product['stock_quantity'] < quantity:
            return generate_response(
                success=False,
                message=f'الكمية المتاحة فقط {product["stock_quantity"]}',
                message_en=f'Only {product["stock_quantity"]} items available'
            ), 400
        
        # Update cart item
        update_response = supabase.table(TABLES['cart_items']).update({
            'quantity': quantity
        }).eq('id', cart_item_id).execute()
        
        if update_response.data:
            return generate_response(
                success=True,
                message='تم تحديث الكمية بنجاح',
                message_en='Quantity updated successfully',
                data={'cart_item': update_response.data[0]}
            )
        
        return generate_response(
            success=False,
            message='فشل في تحديث الكمية',
            message_en='Failed to update quantity'
        ), 500
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في تحديث الكمية',
            message_en='Error updating quantity',
            error=str(e)
        ), 500

@cart_bp.route('/remove/<cart_item_id>', methods=['DELETE'])
@jwt_required()
def remove_cart_item(cart_item_id):
    """Remove item from cart"""
    try:
        user_id = get_jwt_identity()
        
        if not is_valid_uuid(cart_item_id):
            return generate_response(
                success=False,
                message='معرف العنصر غير صحيح',
                message_en='Invalid cart item ID'
            ), 400
        
        supabase = get_supabase_client()
        
        # Verify cart item exists and belongs to user
        cart_item_response = supabase.table(TABLES['cart_items']).select('id').eq('id', cart_item_id).eq('user_id', user_id).execute()
        
        if not cart_item_response.data:
            return generate_response(
                success=False,
                message='عنصر السلة غير موجود',
                message_en='Cart item not found'
            ), 404
        
        # Remove cart item
        delete_response = supabase.table(TABLES['cart_items']).delete().eq('id', cart_item_id).execute()
        
        return generate_response(
            success=True,
            message='تم حذف المنتج من السلة بنجاح',
            message_en='Product removed from cart successfully'
        )
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في حذف المنتج من السلة',
            message_en='Error removing product from cart',
            error=str(e)
        ), 500

@cart_bp.route('/clear', methods=['DELETE'])
@jwt_required()
def clear_cart():
    """Clear all items from cart"""
    try:
        user_id = get_jwt_identity()
        supabase = get_supabase_client()
        
        # Remove all cart items for user
        delete_response = supabase.table(TABLES['cart_items']).delete().eq('user_id', user_id).execute()
        
        return generate_response(
            success=True,
            message='تم تفريغ السلة بنجاح',
            message_en='Cart cleared successfully'
        )
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في تفريغ السلة',
            message_en='Error clearing cart',
            error=str(e)
        ), 500
