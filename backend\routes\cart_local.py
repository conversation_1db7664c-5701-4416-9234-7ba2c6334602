"""
Cart routes using local database adapter
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from config.database_adapter import db
import json

cart_bp = Blueprint('cart', __name__)

@cart_bp.route('/', methods=['GET'])
@jwt_required()
def get_cart():
    """Get user's cart items"""
    try:
        user_id = get_jwt_identity()
        
        # Get cart items with product details
        with db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('''
                SELECT ci.*, p.name_ar, p.name_en, p.price, p.sale_price, p.images, p.stock_quantity
                FROM cart_items ci
                JOIN products p ON ci.product_id = p.id
                WHERE ci.user_id = ? AND p.is_active = 1
                ORDER BY ci.created_at DESC
            ''', [user_id])
            
            cart_items = cursor.fetchall()
        
        # Format cart items
        cart_list = []
        total_amount = 0
        
        for item in cart_items:
            item_dict = dict(item)
            
            # Parse images
            try:
                item_dict['images'] = json.loads(item_dict['images'])
            except:
                item_dict['images'] = []
            
            # Calculate item total
            price = item_dict['sale_price'] or item_dict['price']
            item_total = price * item_dict['quantity']
            item_dict['item_total'] = item_total
            total_amount += item_total
            
            cart_list.append(item_dict)
        
        return jsonify({
            'success': True,
            'data': {
                'items': cart_list,
                'total_amount': total_amount,
                'items_count': len(cart_list)
            },
            'message': 'تم جلب عربة التسوق بنجاح',
            'message_en': 'Cart retrieved successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'خطأ في جلب عربة التسوق',
            'message': str(e)
        }), 500

@cart_bp.route('/add', methods=['POST'])
@jwt_required()
def add_to_cart():
    """Add item to cart"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        product_id = data.get('product_id')
        quantity = int(data.get('quantity', 1))
        
        if not product_id or quantity <= 0:
            return jsonify({
                'success': False,
                'error': 'معرف المنتج والكمية مطلوبان',
                'message': 'Product ID and quantity are required'
            }), 400
        
        # Check if product exists and is active
        products = db.select('products', where='id = ? AND is_active = 1', params=[product_id])
        if not products:
            return jsonify({
                'success': False,
                'error': 'المنتج غير موجود',
                'message': 'Product not found'
            }), 404
        
        product = dict(products[0])
        
        # Check stock
        if product['stock_quantity'] < quantity:
            return jsonify({
                'success': False,
                'error': 'الكمية المطلوبة غير متوفرة',
                'message': 'Insufficient stock'
            }), 400
        
        # Check if item already in cart
        existing_items = db.select('cart_items', where='user_id = ? AND product_id = ?', params=[user_id, product_id])
        
        if existing_items:
            # Update quantity
            existing_item = dict(existing_items[0])
            new_quantity = existing_item['quantity'] + quantity
            
            if product['stock_quantity'] < new_quantity:
                return jsonify({
                    'success': False,
                    'error': 'الكمية المطلوبة غير متوفرة',
                    'message': 'Insufficient stock'
                }), 400
            
            db.update('cart_items', {'quantity': new_quantity}, 'id = ?', [existing_item['id']])
        else:
            # Add new item
            cart_data = {
                'user_id': user_id,
                'product_id': product_id,
                'quantity': quantity
            }
            db.insert('cart_items', cart_data)
        
        return jsonify({
            'success': True,
            'message': 'تم إضافة المنتج للعربة بنجاح',
            'message_en': 'Product added to cart successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'خطأ في إضافة المنتج للعربة',
            'message': str(e)
        }), 500

@cart_bp.route('/update/<item_id>', methods=['PUT'])
@jwt_required()
def update_cart_item(item_id):
    """Update cart item quantity"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        quantity = int(data.get('quantity', 1))
        
        if quantity <= 0:
            return jsonify({
                'success': False,
                'error': 'الكمية يجب أن تكون أكبر من صفر',
                'message': 'Quantity must be greater than zero'
            }), 400
        
        # Check if cart item exists and belongs to user
        cart_items = db.select('cart_items', where='id = ? AND user_id = ?', params=[item_id, user_id])
        if not cart_items:
            return jsonify({
                'success': False,
                'error': 'عنصر العربة غير موجود',
                'message': 'Cart item not found'
            }), 404
        
        cart_item = dict(cart_items[0])
        
        # Check product stock
        products = db.select('products', where='id = ?', params=[cart_item['product_id']])
        if products:
            product = dict(products[0])
            if product['stock_quantity'] < quantity:
                return jsonify({
                    'success': False,
                    'error': 'الكمية المطلوبة غير متوفرة',
                    'message': 'Insufficient stock'
                }), 400
        
        # Update quantity
        db.update('cart_items', {'quantity': quantity}, 'id = ?', [item_id])
        
        return jsonify({
            'success': True,
            'message': 'تم تحديث الكمية بنجاح',
            'message_en': 'Quantity updated successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'خطأ في تحديث الكمية',
            'message': str(e)
        }), 500

@cart_bp.route('/remove/<item_id>', methods=['DELETE'])
@jwt_required()
def remove_from_cart(item_id):
    """Remove item from cart"""
    try:
        user_id = get_jwt_identity()
        
        # Check if cart item exists and belongs to user
        cart_items = db.select('cart_items', where='id = ? AND user_id = ?', params=[item_id, user_id])
        if not cart_items:
            return jsonify({
                'success': False,
                'error': 'عنصر العربة غير موجود',
                'message': 'Cart item not found'
            }), 404
        
        # Remove item
        db.delete('cart_items', 'id = ?', [item_id])
        
        return jsonify({
            'success': True,
            'message': 'تم حذف المنتج من العربة بنجاح',
            'message_en': 'Product removed from cart successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'خطأ في حذف المنتج من العربة',
            'message': str(e)
        }), 500

@cart_bp.route('/clear', methods=['DELETE'])
@jwt_required()
def clear_cart():
    """Clear all items from cart"""
    try:
        user_id = get_jwt_identity()
        
        db.delete('cart_items', 'user_id = ?', [user_id])
        
        return jsonify({
            'success': True,
            'message': 'تم تفريغ العربة بنجاح',
            'message_en': 'Cart cleared successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'خطأ في تفريغ العربة',
            'message': str(e)
        }), 500