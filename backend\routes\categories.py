"""
Category routes for managing product categories
"""

from flask import Blueprint, request, jsonify
from config.database import get_supabase_client, TABLES
from utils.helpers import generate_response, is_valid_uuid

categories_bp = Blueprint('categories', __name__)

@categories_bp.route('/', methods=['GET'])
def get_categories():
    """Get all active categories with hierarchy"""
    try:
        include_products = request.args.get('include_products', 'false').lower() == 'true'
        
        supabase = get_supabase_client()
        
        # Get all categories
        if include_products:
            query = supabase.table(TABLES['categories']).select(
                'id, name_ar, name_en, description_ar, description_en, image_url, parent_id, sort_order, '
                'products(id, name_ar, name_en, price, sale_price, images)'
            ).eq('is_active', True).order('sort_order')
        else:
            query = supabase.table(TABLES['categories']).select(
                'id, name_ar, name_en, description_ar, description_en, image_url, parent_id, sort_order'
            ).eq('is_active', True).order('sort_order')
        
        response = query.execute()
        
        # Organize categories into hierarchy
        categories = response.data
        category_map = {cat['id']: cat for cat in categories}
        root_categories = []
        
        for category in categories:
            category['children'] = []
            if category['parent_id'] is None:
                root_categories.append(category)
            else:
                parent = category_map.get(category['parent_id'])
                if parent:
                    parent['children'].append(category)
        
        return generate_response(
            success=True,
            message='تم جلب الفئات بنجاح',
            message_en='Categories retrieved successfully',
            data={'categories': root_categories}
        )
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في جلب الفئات',
            message_en='Error retrieving categories',
            error=str(e)
        ), 500

@categories_bp.route('/<category_id>', methods=['GET'])
def get_category(category_id):
    """Get single category by ID"""
    try:
        if not is_valid_uuid(category_id):
            return generate_response(
                success=False,
                message='معرف الفئة غير صحيح',
                message_en='Invalid category ID'
            ), 400
        
        supabase = get_supabase_client()
        
        # Get category details
        category_response = supabase.table(TABLES['categories']).select(
            'id, name_ar, name_en, description_ar, description_en, image_url, parent_id, sort_order'
        ).eq('id', category_id).eq('is_active', True).execute()
        
        if not category_response.data:
            return generate_response(
                success=False,
                message='الفئة غير موجودة',
                message_en='Category not found'
            ), 404
        
        category = category_response.data[0]
        
        # Get subcategories
        subcategories_response = supabase.table(TABLES['categories']).select(
            'id, name_ar, name_en, description_ar, description_en, image_url, sort_order'
        ).eq('parent_id', category_id).eq('is_active', True).order('sort_order').execute()
        
        # Get parent category if exists
        parent_category = None
        if category['parent_id']:
            parent_response = supabase.table(TABLES['categories']).select(
                'id, name_ar, name_en'
            ).eq('id', category['parent_id']).eq('is_active', True).execute()
            if parent_response.data:
                parent_category = parent_response.data[0]
        
        # Get product count in this category
        product_count_response = supabase.table(TABLES['products']).select(
            'id', count='exact'
        ).eq('category_id', category_id).eq('is_active', True).execute()
        
        product_count = product_count_response.count if product_count_response.count is not None else 0
        
        return generate_response(
            success=True,
            message='تم جلب الفئة بنجاح',
            message_en='Category retrieved successfully',
            data={
                'category': category,
                'subcategories': subcategories_response.data,
                'parent_category': parent_category,
                'product_count': product_count
            }
        )
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في جلب الفئة',
            message_en='Error retrieving category',
            error=str(e)
        ), 500

@categories_bp.route('/tree', methods=['GET'])
def get_category_tree():
    """Get complete category tree structure"""
    try:
        supabase = get_supabase_client()
        
        # Get all categories with product counts
        categories_response = supabase.table(TABLES['categories']).select(
            'id, name_ar, name_en, description_ar, description_en, image_url, parent_id, sort_order'
        ).eq('is_active', True).order('sort_order').execute()
        
        categories = categories_response.data
        
        # Get product counts for each category
        for category in categories:
            product_count_response = supabase.table(TABLES['products']).select(
                'id', count='exact'
            ).eq('category_id', category['id']).eq('is_active', True).execute()
            
            category['product_count'] = product_count_response.count if product_count_response.count is not None else 0
        
        # Build tree structure
        def build_tree(parent_id=None):
            tree = []
            for category in categories:
                if category['parent_id'] == parent_id:
                    category_node = category.copy()
                    category_node['children'] = build_tree(category['id'])
                    tree.append(category_node)
            return tree
        
        tree = build_tree()
        
        return generate_response(
            success=True,
            message='تم جلب شجرة الفئات بنجاح',
            message_en='Category tree retrieved successfully',
            data={'category_tree': tree}
        )
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في جلب شجرة الفئات',
            message_en='Error retrieving category tree',
            error=str(e)
        ), 500

@categories_bp.route('/popular', methods=['GET'])
def get_popular_categories():
    """Get popular categories based on product count"""
    try:
        limit = min(int(request.args.get('limit', 6)), 20)
        
        supabase = get_supabase_client()
        
        # Get categories with their product counts
        categories_response = supabase.table(TABLES['categories']).select(
            'id, name_ar, name_en, description_ar, description_en, image_url'
        ).eq('is_active', True).execute()
        
        categories_with_counts = []
        
        for category in categories_response.data:
            # Get product count for each category
            product_count_response = supabase.table(TABLES['products']).select(
                'id', count='exact'
            ).eq('category_id', category['id']).eq('is_active', True).execute()
            
            product_count = product_count_response.count if product_count_response.count is not None else 0
            
            if product_count > 0:  # Only include categories with products
                category['product_count'] = product_count
                categories_with_counts.append(category)
        
        # Sort by product count and take top categories
        popular_categories = sorted(
            categories_with_counts,
            key=lambda x: x['product_count'],
            reverse=True
        )[:limit]
        
        return generate_response(
            success=True,
            message='تم جلب الفئات الشائعة بنجاح',
            message_en='Popular categories retrieved successfully',
            data={'categories': popular_categories}
        )
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في جلب الفئات الشائعة',
            message_en='Error retrieving popular categories',
            error=str(e)
        ), 500

@categories_bp.route('/<category_id>/products', methods=['GET'])
def get_category_products(category_id):
    """Get products in a specific category"""
    try:
        if not is_valid_uuid(category_id):
            return generate_response(
                success=False,
                message='معرف الفئة غير صحيح',
                message_en='Invalid category ID'
            ), 400
        
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 50)
        sort_by = request.args.get('sort_by', 'created_at')
        sort_order = request.args.get('sort_order', 'desc')
        
        supabase = get_supabase_client()
        
        # Verify category exists
        category_response = supabase.table(TABLES['categories']).select('id, name_ar, name_en').eq('id', category_id).eq('is_active', True).execute()
        
        if not category_response.data:
            return generate_response(
                success=False,
                message='الفئة غير موجودة',
                message_en='Category not found'
            ), 404
        
        category = category_response.data[0]
        
        # Get products in category
        query = supabase.table(TABLES['products']).select(
            'id, name_ar, name_en, short_description_ar, short_description_en, '
            'price, sale_price, images, is_featured, stock_quantity'
        ).eq('category_id', category_id).eq('is_active', True)
        
        # Apply sorting
        valid_sort_fields = ['created_at', 'price', 'name_ar', 'name_en']
        if sort_by in valid_sort_fields:
            ascending = sort_order.lower() == 'asc'
            query = query.order(sort_by, desc=not ascending)
        else:
            query = query.order('created_at', desc=True)
        
        # Get total count
        count_response = supabase.table(TABLES['products']).select('id', count='exact').eq('category_id', category_id).eq('is_active', True).execute()
        total_count = count_response.count if count_response.count is not None else 0
        
        # Apply pagination
        offset = (page - 1) * per_page
        query = query.range(offset, offset + per_page - 1)
        
        products_response = query.execute()
        
        # Calculate pagination info
        total_pages = (total_count + per_page - 1) // per_page
        
        return generate_response(
            success=True,
            message='تم جلب منتجات الفئة بنجاح',
            message_en='Category products retrieved successfully',
            data={
                'category': category,
                'products': products_response.data,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total_count,
                    'total_pages': total_pages,
                    'has_next': page < total_pages,
                    'has_prev': page > 1
                }
            }
        )
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في جلب منتجات الفئة',
            message_en='Error retrieving category products',
            error=str(e)
        ), 500
