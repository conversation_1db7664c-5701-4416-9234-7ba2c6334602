from flask import Blueprint, request, jsonify
from config.local_database import get_db_connection

categories_bp = Blueprint('categories', __name__)

@categories_bp.route('/', methods=['GET'])
def get_categories():
    """Get all active categories"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            query = """
                SELECT
                    c.id, c.name_ar, c.name_en, c.description_ar, c.description_en,
                    c.image_url, c.sort_order, c.created_at,
                    COUNT(p.id) as product_count
                FROM categories c
                LEFT JOIN products p ON c.id = p.category_id AND p.is_active = 1
                WHERE c.is_active = 1
                GROUP BY c.id, c.name_ar, c.name_en, c.description_ar, c.description_en,
                         c.image_url, c.sort_order, c.created_at
                ORDER BY c.sort_order, c.name_ar
            """

            cursor.execute(query)
            categories = cursor.fetchall()

            formatted_categories = []
            for category in categories:
                category_dict = dict(category)
                formatted_categories.append(category_dict)

            return jsonify({
                'success': True,
                'data': {
                    'categories': formatted_categories
                },
                'message': 'تم جلب الفئات بنجاح'
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'خطأ في جلب الفئات',
            'message': str(e)
        }), 500

@categories_bp.route('/popular', methods=['GET'])
def get_popular_categories():
    """Get popular categories with most products"""
    try:
        limit = min(int(request.args.get('limit', 6)), 20)

        with get_db_connection() as conn:
            cursor = conn.cursor()

            query = """
                SELECT
                    c.id, c.name_ar, c.name_en, c.description_ar, c.description_en,
                    c.image_url, c.sort_order, c.created_at,
                    COUNT(p.id) as product_count
                FROM categories c
                LEFT JOIN products p ON c.id = p.category_id AND p.is_active = 1
                WHERE c.is_active = 1
                GROUP BY c.id, c.name_ar, c.name_en, c.description_ar, c.description_en,
                         c.image_url, c.sort_order, c.created_at
                HAVING COUNT(p.id) > 0
                ORDER BY COUNT(p.id) DESC, c.name_ar
                LIMIT ?
            """

            cursor.execute(query, [limit])
            categories = cursor.fetchall()

            formatted_categories = []
            for category in categories:
                category_dict = dict(category)
                formatted_categories.append(category_dict)

            return jsonify({
                'success': True,
                'data': {
                    'categories': formatted_categories
                },
                'message': 'تم جلب الفئات الشائعة بنجاح'
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'خطأ في جلب الفئات الشائعة',
            'message': str(e)
        }), 500

@categories_bp.route('/<int:category_id>', methods=['GET'])
def get_category(category_id):
    """Get single category by ID"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            query = """
                SELECT
                    c.id, c.name_ar, c.name_en, c.description_ar, c.description_en,
                    c.image_url, c.sort_order, c.created_at,
                    COUNT(p.id) as product_count
                FROM categories c
                LEFT JOIN products p ON c.id = p.category_id AND p.is_active = 1
                WHERE c.id = ? AND c.is_active = 1
                GROUP BY c.id, c.name_ar, c.name_en, c.description_ar, c.description_en,
                         c.image_url, c.sort_order, c.created_at
            """

            cursor.execute(query, [category_id])
            category = cursor.fetchone()

            if not category:
                return jsonify({
                    'success': False,
                    'error': 'الفئة غير موجودة',
                    'message': 'Category not found'
                }), 404

            category_dict = dict(category)

            return jsonify({
                'success': True,
                'data': {
                    'category': category_dict
                },
                'message': 'تم جلب الفئة بنجاح'
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'خطأ في جلب الفئة',
            'message': str(e)
        }), 500