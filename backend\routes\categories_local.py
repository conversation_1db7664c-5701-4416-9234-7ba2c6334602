"""
Categories routes using local database adapter
"""

from flask import Blueprint, request, jsonify
from config.database_adapter import db

categories_bp = Blueprint('categories', __name__)

@categories_bp.route('/', methods=['GET'])
def get_categories():
    """Get all active categories"""
    try:
        categories = db.select(
            'categories',
            where='is_active = 1',
            order_by='sort_order ASC, name_ar ASC'
        )
        
        categories_list = []
        for category in categories:
            category_dict = dict(category)
            category_dict['is_active'] = bool(category_dict['is_active'])
            categories_list.append(category_dict)
        
        return jsonify({
            'success': True,
            'data': categories_list,
            'message': 'تم جلب الفئات بنجاح',
            'message_en': 'Categories retrieved successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'خطأ في جلب الفئات',
            'message': str(e)
        }), 500

@categories_bp.route('/<category_id>', methods=['GET'])
def get_category(category_id):
    """Get single category by ID"""
    try:
        categories = db.select('categories', where='id = ? AND is_active = 1', params=[category_id])
        
        if not categories:
            return jsonify({
                'success': False,
                'error': 'الفئة غير موجودة',
                'message': 'Category not found'
            }), 404
        
        category = dict(categories[0])
        category['is_active'] = bool(category['is_active'])
        
        # Get products count in this category
        with db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) as count FROM products WHERE category_id = ? AND is_active = 1', [category_id])
            products_count = cursor.fetchone()['count']
        
        category['products_count'] = products_count
        
        return jsonify({
            'success': True,
            'data': category,
            'message': 'تم جلب الفئة بنجاح',
            'message_en': 'Category retrieved successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'خطأ في جلب الفئة',
            'message': str(e)
        }), 500

@categories_bp.route('/<category_id>/products', methods=['GET'])
def get_category_products(category_id):
    """Get products in a specific category"""
    try:
        # Verify category exists
        categories = db.select('categories', where='id = ? AND is_active = 1', params=[category_id])
        if not categories:
            return jsonify({
                'success': False,
                'error': 'الفئة غير موجودة',
                'message': 'Category not found'
            }), 404
        
        # Get pagination parameters
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 50)
        sort_by = request.args.get('sort_by', 'created_at')
        sort_order = request.args.get('sort_order', 'desc')
        
        # Build ORDER BY clause
        valid_sort_fields = ['name_ar', 'name_en', 'price', 'created_at']
        if sort_by not in valid_sort_fields:
            sort_by = 'created_at'
        
        sort_order = 'DESC' if sort_order.upper() == 'DESC' else 'ASC'
        order_by = f"{sort_by} {sort_order}"
        
        # Get total count
        with db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute('SELECT COUNT(*) as count FROM products WHERE category_id = ? AND is_active = 1', [category_id])
            total_count = cursor.fetchone()['count']
        
        # Calculate pagination
        import math
        offset = (page - 1) * per_page
        total_pages = math.ceil(total_count / per_page)
        
        # Get products
        products = db.select(
            'products',
            where='category_id = ? AND is_active = 1',
            params=[category_id],
            limit=per_page,
            offset=offset,
            order_by=order_by
        )
        
        # Convert to list and parse JSON fields
        products_list = []
        for product in products:
            product_dict = dict(product)
            try:
                import json
                product_dict['images'] = json.loads(product_dict['images'])
            except:
                product_dict['images'] = []
            
            product_dict['is_active'] = bool(product_dict['is_active'])
            product_dict['is_featured'] = bool(product_dict['is_featured'])
            
            products_list.append(product_dict)
        
        category = dict(categories[0])
        
        return jsonify({
            'success': True,
            'data': {
                'category': category,
                'products': products_list,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total_count,
                    'pages': total_pages,
                    'has_next': page < total_pages,
                    'has_prev': page > 1
                }
            },
            'message': 'تم جلب منتجات الفئة بنجاح',
            'message_en': 'Category products retrieved successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'خطأ في جلب منتجات الفئة',
            'message': str(e)
        }), 500