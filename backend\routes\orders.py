"""
Order routes for managing customer orders
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from config.database import get_supabase_client, TABLES
from utils.helpers import generate_response, is_valid_uuid, calculate_tax, calculate_shipping_cost, get_order_status_ar
from utils.validators import validate_address_data, validate_order_status

orders_bp = Blueprint('orders', __name__)

@orders_bp.route('/', methods=['GET'])
@jwt_required()
def get_user_orders():
    """Get user's orders"""
    try:
        user_id = get_jwt_identity()
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 10)), 50)
        status = request.args.get('status')
        
        supabase = get_supabase_client()
        
        # Build query
        query = supabase.table(TABLES['orders']).select(
            'id, order_number, status, payment_status, subtotal, tax_amount, '
            'shipping_amount, total_amount, created_at, shipped_at, delivered_at'
        ).eq('user_id', user_id)
        
        # Filter by status if provided
        if status and validate_order_status(status):
            query = query.eq('status', status)
        
        # Apply pagination and ordering
        offset = (page - 1) * per_page
        query = query.order('created_at', desc=True).range(offset, offset + per_page - 1)
        
        # Get total count
        count_query = supabase.table(TABLES['orders']).select('id', count='exact').eq('user_id', user_id)
        if status and validate_order_status(status):
            count_query = count_query.eq('status', status)
        
        count_response = count_query.execute()
        total_count = count_response.count if count_response.count is not None else 0
        
        # Execute main query
        response = query.execute()
        
        # Add Arabic status translations
        for order in response.data:
            order['status_ar'] = get_order_status_ar(order['status'])
        
        # Calculate pagination info
        total_pages = (total_count + per_page - 1) // per_page
        
        return generate_response(
            success=True,
            message='تم جلب الطلبات بنجاح',
            message_en='Orders retrieved successfully',
            data={
                'orders': response.data,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total_count,
                    'total_pages': total_pages,
                    'has_next': page < total_pages,
                    'has_prev': page > 1
                }
            }
        )
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في جلب الطلبات',
            message_en='Error retrieving orders',
            error=str(e)
        ), 500

@orders_bp.route('/<order_id>', methods=['GET'])
@jwt_required()
def get_order(order_id):
    """Get single order details"""
    try:
        user_id = get_jwt_identity()
        
        if not is_valid_uuid(order_id):
            return generate_response(
                success=False,
                message='معرف الطلب غير صحيح',
                message_en='Invalid order ID'
            ), 400
        
        supabase = get_supabase_client()
        
        # Get order details
        order_response = supabase.table(TABLES['orders']).select(
            'id, order_number, status, payment_status, payment_method, '
            'subtotal, tax_amount, shipping_amount, discount_amount, total_amount, '
            'currency, notes, shipping_address, billing_address, tracking_number, '
            'shipped_at, delivered_at, created_at, updated_at'
        ).eq('id', order_id).eq('user_id', user_id).execute()
        
        if not order_response.data:
            return generate_response(
                success=False,
                message='الطلب غير موجود',
                message_en='Order not found'
            ), 404
        
        order = order_response.data[0]
        order['status_ar'] = get_order_status_ar(order['status'])
        
        # Get order items
        items_response = supabase.table(TABLES['order_items']).select(
            'id, product_name_ar, product_name_en, product_sku, quantity, unit_price, total_price, '
            'products(id, images)'
        ).eq('order_id', order_id).execute()
        
        return generate_response(
            success=True,
            message='تم جلب تفاصيل الطلب بنجاح',
            message_en='Order details retrieved successfully',
            data={
                'order': order,
                'items': items_response.data
            }
        )
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في جلب تفاصيل الطلب',
            message_en='Error retrieving order details',
            error=str(e)
        ), 500

@orders_bp.route('/create', methods=['POST'])
@jwt_required()
def create_order():
    """Create new order from cart"""
    try:
        user_id = get_jwt_identity()
        data = request.get_json()
        
        shipping_address = data.get('shipping_address')
        billing_address = data.get('billing_address')
        payment_method = data.get('payment_method', 'cash_on_delivery')
        notes = data.get('notes', '')
        
        # Validate addresses
        if not shipping_address:
            return generate_response(
                success=False,
                message='عنوان الشحن مطلوب',
                message_en='Shipping address is required'
            ), 400
        
        is_valid, error_msg = validate_address_data(shipping_address)
        if not is_valid:
            return generate_response(
                success=False,
                message=error_msg,
                message_en='Invalid shipping address'
            ), 400
        
        # Use shipping address as billing if not provided
        if not billing_address:
            billing_address = shipping_address
        else:
            is_valid, error_msg = validate_address_data(billing_address)
            if not is_valid:
                return generate_response(
                    success=False,
                    message=error_msg,
                    message_en='Invalid billing address'
                ), 400
        
        supabase = get_supabase_client()
        
        # Get cart items
        cart_response = supabase.table(TABLES['cart_items']).select(
            'id, quantity, '
            'products(id, name_ar, name_en, price, sale_price, sku, stock_quantity, weight)'
        ).eq('user_id', user_id).execute()
        
        if not cart_response.data:
            return generate_response(
                success=False,
                message='السلة فارغة',
                message_en='Cart is empty'
            ), 400
        
        cart_items = cart_response.data
        
        # Validate stock and calculate totals
        order_items = []
        subtotal = 0
        total_weight = 0
        
        for cart_item in cart_items:
            product = cart_item['products']
            quantity = cart_item['quantity']
            
            # Check stock
            if product['stock_quantity'] < quantity:
                return generate_response(
                    success=False,
                    message=f'المنتج {product["name_ar"]} غير متوفر بالكمية المطلوبة',
                    message_en=f'Product {product["name_en"]} not available in requested quantity'
                ), 400
            
            # Calculate price
            unit_price = product['sale_price'] if product['sale_price'] else product['price']
            total_price = unit_price * quantity
            
            order_items.append({
                'product_id': product['id'],
                'product_name_ar': product['name_ar'],
                'product_name_en': product['name_en'],
                'product_sku': product['sku'],
                'quantity': quantity,
                'unit_price': unit_price,
                'total_price': total_price
            })
            
            subtotal += total_price
            if product.get('weight'):
                total_weight += product['weight'] * quantity
        
        # Calculate tax and shipping
        tax_amount = calculate_tax(subtotal)
        shipping_amount = calculate_shipping_cost(subtotal, total_weight)
        total_amount = subtotal + tax_amount + shipping_amount
        
        # Create order
        order_data = {
            'user_id': user_id,
            'status': 'pending',
            'payment_status': 'pending',
            'payment_method': payment_method,
            'subtotal': subtotal,
            'tax_amount': tax_amount,
            'shipping_amount': shipping_amount,
            'discount_amount': 0,
            'total_amount': total_amount,
            'currency': 'SAR',
            'notes': notes,
            'shipping_address': shipping_address,
            'billing_address': billing_address
        }
        
        order_response = supabase.table(TABLES['orders']).insert(order_data).execute()
        
        if not order_response.data:
            return generate_response(
                success=False,
                message='فشل في إنشاء الطلب',
                message_en='Failed to create order'
            ), 500
        
        order = order_response.data[0]
        order_id = order['id']
        
        # Create order items
        for item in order_items:
            item['order_id'] = order_id
        
        items_response = supabase.table(TABLES['order_items']).insert(order_items).execute()
        
        if not items_response.data:
            # Rollback order creation
            supabase.table(TABLES['orders']).delete().eq('id', order_id).execute()
            return generate_response(
                success=False,
                message='فشل في إنشاء عناصر الطلب',
                message_en='Failed to create order items'
            ), 500
        
        # Update product stock
        for cart_item in cart_items:
            product = cart_item['products']
            new_stock = product['stock_quantity'] - cart_item['quantity']
            supabase.table(TABLES['products']).update({
                'stock_quantity': new_stock
            }).eq('id', product['id']).execute()
        
        # Clear cart
        supabase.table(TABLES['cart_items']).delete().eq('user_id', user_id).execute()
        
        return generate_response(
            success=True,
            message='تم إنشاء الطلب بنجاح',
            message_en='Order created successfully',
            data={
                'order': order,
                'items': items_response.data
            }
        ), 201
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في إنشاء الطلب',
            message_en='Error creating order',
            error=str(e)
        ), 500

@orders_bp.route('/<order_id>/cancel', methods=['PUT'])
@jwt_required()
def cancel_order(order_id):
    """Cancel an order"""
    try:
        user_id = get_jwt_identity()
        
        if not is_valid_uuid(order_id):
            return generate_response(
                success=False,
                message='معرف الطلب غير صحيح',
                message_en='Invalid order ID'
            ), 400
        
        supabase = get_supabase_client()
        
        # Get order
        order_response = supabase.table(TABLES['orders']).select(
            'id, status, user_id'
        ).eq('id', order_id).eq('user_id', user_id).execute()
        
        if not order_response.data:
            return generate_response(
                success=False,
                message='الطلب غير موجود',
                message_en='Order not found'
            ), 404
        
        order = order_response.data[0]
        
        # Check if order can be cancelled
        if order['status'] in ['shipped', 'delivered', 'cancelled', 'refunded']:
            return generate_response(
                success=False,
                message='لا يمكن إلغاء هذا الطلب',
                message_en='This order cannot be cancelled'
            ), 400
        
        # Update order status
        update_response = supabase.table(TABLES['orders']).update({
            'status': 'cancelled'
        }).eq('id', order_id).execute()
        
        if update_response.data:
            # Restore product stock
            items_response = supabase.table(TABLES['order_items']).select(
                'product_id, quantity'
            ).eq('order_id', order_id).execute()
            
            for item in items_response.data:
                if item['product_id']:
                    # Get current stock
                    product_response = supabase.table(TABLES['products']).select(
                        'stock_quantity'
                    ).eq('id', item['product_id']).execute()
                    
                    if product_response.data:
                        current_stock = product_response.data[0]['stock_quantity']
                        new_stock = current_stock + item['quantity']
                        
                        supabase.table(TABLES['products']).update({
                            'stock_quantity': new_stock
                        }).eq('id', item['product_id']).execute()
            
            return generate_response(
                success=True,
                message='تم إلغاء الطلب بنجاح',
                message_en='Order cancelled successfully',
                data={'order': update_response.data[0]}
            )
        
        return generate_response(
            success=False,
            message='فشل في إلغاء الطلب',
            message_en='Failed to cancel order'
        ), 500
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في إلغاء الطلب',
            message_en='Error cancelling order',
            error=str(e)
        ), 500
