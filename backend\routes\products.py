"""
Product routes for listing, searching, and managing products
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from config.database import get_supabase_client, TABLES
from utils.helpers import generate_response, paginate_query, build_search_query, is_valid_uuid
from utils.validators import validate_search_query

products_bp = Blueprint('products', __name__)

@products_bp.route('/', methods=['GET'])
def get_products():
    """Get products with filtering, searching, and pagination"""
    try:
        # Get query parameters
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 50)  # Max 50 items per page
        category_id = request.args.get('category_id')
        search = request.args.get('search', '').strip()
        sort_by = request.args.get('sort_by', 'created_at')
        sort_order = request.args.get('sort_order', 'desc')
        min_price = request.args.get('min_price')
        max_price = request.args.get('max_price')
        is_featured = request.args.get('is_featured')
        
        supabase = get_supabase_client()
        
        # Build base query
        query = supabase.table(TABLES['products']).select(
            'id, name_ar, name_en, description_ar, description_en, short_description_ar, short_description_en, '
            'price, sale_price, sku, stock_quantity, images, is_featured, '
            'categories(id, name_ar, name_en), created_at'
        ).eq('is_active', True)
        
        # Apply filters
        if category_id and is_valid_uuid(category_id):
            query = query.eq('category_id', category_id)
        
        if min_price:
            try:
                query = query.gte('price', float(min_price))
            except ValueError:
                pass
        
        if max_price:
            try:
                query = query.lte('price', float(max_price))
            except ValueError:
                pass
        
        if is_featured and is_featured.lower() == 'true':
            query = query.eq('is_featured', True)
        
        # Apply search
        if search and validate_search_query(search):
            search_fields = ['name_ar', 'name_en', 'description_ar', 'description_en']
            search_condition = build_search_query(search, search_fields)
            if search_condition:
                query = query.or_(search_condition)
        
        # Apply sorting
        valid_sort_fields = ['created_at', 'price', 'name_ar', 'name_en']
        if sort_by in valid_sort_fields:
            ascending = sort_order.lower() == 'asc'
            query = query.order(sort_by, desc=not ascending)
        else:
            query = query.order('created_at', desc=True)
        
        # Get total count for pagination
        count_query = supabase.table(TABLES['products']).select('id', count='exact').eq('is_active', True)
        
        # Apply same filters to count query
        if category_id and is_valid_uuid(category_id):
            count_query = count_query.eq('category_id', category_id)
        if min_price:
            try:
                count_query = count_query.gte('price', float(min_price))
            except ValueError:
                pass
        if max_price:
            try:
                count_query = count_query.lte('price', float(max_price))
            except ValueError:
                pass
        if is_featured and is_featured.lower() == 'true':
            count_query = count_query.eq('is_featured', True)
        if search and validate_search_query(search):
            search_fields = ['name_ar', 'name_en', 'description_ar', 'description_en']
            search_condition = build_search_query(search, search_fields)
            if search_condition:
                count_query = count_query.or_(search_condition)
        
        count_response = count_query.execute()
        total_count = count_response.count if count_response.count is not None else 0
        
        # Apply pagination
        query = paginate_query(query, page, per_page)
        
        # Execute query
        response = query.execute()
        
        # Calculate pagination info
        total_pages = (total_count + per_page - 1) // per_page
        has_next = page < total_pages
        has_prev = page > 1
        
        return generate_response(
            success=True,
            message='تم جلب المنتجات بنجاح',
            message_en='Products retrieved successfully',
            data={
                'products': response.data,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total_count,
                    'total_pages': total_pages,
                    'has_next': has_next,
                    'has_prev': has_prev
                }
            }
        )
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في جلب المنتجات',
            message_en='Error retrieving products',
            error=str(e)
        ), 500

@products_bp.route('/<product_id>', methods=['GET'])
def get_product(product_id):
    """Get single product by ID"""
    try:
        if not is_valid_uuid(product_id):
            return generate_response(
                success=False,
                message='معرف المنتج غير صحيح',
                message_en='Invalid product ID'
            ), 400
        
        supabase = get_supabase_client()
        
        # Get product with category and reviews
        product_response = supabase.table(TABLES['products']).select(
            'id, name_ar, name_en, description_ar, description_en, short_description_ar, short_description_en, '
            'price, sale_price, sku, stock_quantity, weight, dimensions, images, tags, is_featured, '
            'meta_title_ar, meta_title_en, meta_description_ar, meta_description_en, '
            'categories(id, name_ar, name_en), created_at, updated_at'
        ).eq('id', product_id).eq('is_active', True).execute()
        
        if not product_response.data:
            return generate_response(
                success=False,
                message='المنتج غير موجود',
                message_en='Product not found'
            ), 404
        
        product = product_response.data[0]
        
        # Get product reviews
        reviews_response = supabase.table(TABLES['reviews']).select(
            'id, rating, title, comment, is_verified, created_at, '
            'users(first_name, last_name)'
        ).eq('product_id', product_id).eq('is_approved', True).order('created_at', desc=True).limit(10).execute()
        
        # Calculate average rating
        if reviews_response.data:
            total_rating = sum(review['rating'] for review in reviews_response.data)
            average_rating = round(total_rating / len(reviews_response.data), 1)
            review_count = len(reviews_response.data)
        else:
            average_rating = 0
            review_count = 0
        
        # Get related products (same category)
        related_products = []
        if product.get('categories'):
            related_response = supabase.table(TABLES['products']).select(
                'id, name_ar, name_en, price, sale_price, images'
            ).eq('category_id', product['categories']['id']).eq('is_active', True).neq('id', product_id).limit(4).execute()
            related_products = related_response.data
        
        return generate_response(
            success=True,
            message='تم جلب المنتج بنجاح',
            message_en='Product retrieved successfully',
            data={
                'product': product,
                'reviews': reviews_response.data,
                'average_rating': average_rating,
                'review_count': review_count,
                'related_products': related_products
            }
        )
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في جلب المنتج',
            message_en='Error retrieving product',
            error=str(e)
        ), 500

@products_bp.route('/featured', methods=['GET'])
def get_featured_products():
    """Get featured products"""
    try:
        limit = min(int(request.args.get('limit', 8)), 20)  # Max 20 featured products
        
        supabase = get_supabase_client()
        
        response = supabase.table(TABLES['products']).select(
            'id, name_ar, name_en, short_description_ar, short_description_en, '
            'price, sale_price, images, categories(name_ar, name_en)'
        ).eq('is_active', True).eq('is_featured', True).order('created_at', desc=True).limit(limit).execute()
        
        return generate_response(
            success=True,
            message='تم جلب المنتجات المميزة بنجاح',
            message_en='Featured products retrieved successfully',
            data={'products': response.data}
        )
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في جلب المنتجات المميزة',
            message_en='Error retrieving featured products',
            error=str(e)
        ), 500

@products_bp.route('/search', methods=['GET'])
def search_products():
    """Search products with advanced filters"""
    try:
        query_param = request.args.get('q', '').strip()
        
        if not query_param or not validate_search_query(query_param):
            return generate_response(
                success=False,
                message='استعلام البحث غير صحيح',
                message_en='Invalid search query'
            ), 400
        
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 50)
        
        supabase = get_supabase_client()
        
        # Search in multiple fields
        search_fields = ['name_ar', 'name_en', 'description_ar', 'description_en', 'tags']
        search_condition = build_search_query(query_param, search_fields)
        
        query = supabase.table(TABLES['products']).select(
            'id, name_ar, name_en, short_description_ar, short_description_en, '
            'price, sale_price, images, categories(name_ar, name_en)'
        ).eq('is_active', True)
        
        if search_condition:
            query = query.or_(search_condition)
        
        # Get total count
        count_query = supabase.table(TABLES['products']).select('id', count='exact').eq('is_active', True)
        if search_condition:
            count_query = count_query.or_(search_condition)
        
        count_response = count_query.execute()
        total_count = count_response.count if count_response.count is not None else 0
        
        # Apply pagination and execute
        query = paginate_query(query, page, per_page)
        response = query.execute()
        
        # Calculate pagination info
        total_pages = (total_count + per_page - 1) // per_page
        
        return generate_response(
            success=True,
            message='تم البحث بنجاح',
            message_en='Search completed successfully',
            data={
                'products': response.data,
                'query': query_param,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total_count,
                    'total_pages': total_pages,
                    'has_next': page < total_pages,
                    'has_prev': page > 1
                }
            }
        )
        
    except Exception as e:
        return generate_response(
            success=False,
            message='خطأ في البحث',
            message_en='Search error',
            error=str(e)
        ), 500
