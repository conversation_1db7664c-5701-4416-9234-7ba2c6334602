"""
Product routes for listing, searching, and managing products
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from config.local_database import get_db_connection
import json
import math

products_bp = Blueprint('products', __name__)

@products_bp.route('/', methods=['GET'])
def get_products():
    """Get products with filtering, searching, and pagination"""
    try:
        # Get query parameters
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 50)  # Max 50 items per page
        category_id = request.args.get('category_id')
        search = request.args.get('search', '').strip()
        sort_by = request.args.get('sort_by', 'created_at')
        sort_order = request.args.get('sort_order', 'desc')
        min_price = request.args.get('min_price')
        max_price = request.args.get('max_price')
        is_featured = request.args.get('is_featured')

        with get_db_connection() as conn:
            cursor = conn.cursor()

            # Build base query
            where_conditions = ["p.is_active = 1"]
            params = []

            # Apply filters
            if category_id:
                where_conditions.append("p.category_id = ?")
                params.append(category_id)

            if min_price:
                try:
                    where_conditions.append("p.price >= ?")
                    params.append(float(min_price))
                except ValueError:
                    pass

            if max_price:
                try:
                    where_conditions.append("p.price <= ?")
                    params.append(float(max_price))
                except ValueError:
                    pass

            if is_featured:
                where_conditions.append("p.is_featured = ?")
                params.append(1 if is_featured.lower() == 'true' else 0)

            if search:
                where_conditions.append("(p.name_ar LIKE ? OR p.name_en LIKE ? OR p.description_ar LIKE ?)")
                search_param = f"%{search}%"
                params.extend([search_param, search_param, search_param])

            where_clause = " AND ".join(where_conditions)

            # Count total products
            count_query = f"""
                SELECT COUNT(*) as total
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE {where_clause}
            """
            cursor.execute(count_query, params)
            total = cursor.fetchone()['total']

            # Calculate pagination
            offset = (page - 1) * per_page
            total_pages = math.ceil(total / per_page)

            # Build main query with pagination
            order_clause = f"p.{sort_by} {'DESC' if sort_order.lower() == 'desc' else 'ASC'}"

            query = f"""
                SELECT
                    p.id, p.name_ar, p.name_en, p.description_ar, p.description_en,
                    p.price, p.sale_price, p.sku, p.stock_quantity, p.images,
                    p.is_featured, p.created_at,
                    c.id as category_id, c.name_ar as category_name_ar, c.name_en as category_name_en
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE {where_clause}
                ORDER BY {order_clause}
                LIMIT ? OFFSET ?
            """

            cursor.execute(query, params + [per_page, offset])
            products = cursor.fetchall()

            # Format products
            formatted_products = []
            for product in products:
                product_dict = dict(product)
                # Parse images JSON
                try:
                    product_dict['images'] = json.loads(product_dict['images']) if product_dict['images'] else []
                except:
                    product_dict['images'] = []

                # Add category info
                if product_dict['category_id']:
                    product_dict['category'] = {
                        'id': product_dict['category_id'],
                        'name_ar': product_dict['category_name_ar'],
                        'name_en': product_dict['category_name_en']
                    }
                else:
                    product_dict['category'] = None

                # Remove redundant fields
                del product_dict['category_id']
                del product_dict['category_name_ar']
                del product_dict['category_name_en']

                formatted_products.append(product_dict)

            return jsonify({
                'success': True,
                'data': {
                    'products': formatted_products,
                    'pagination': {
                        'page': page,
                        'per_page': per_page,
                        'total': total,
                        'total_pages': total_pages,
                        'has_next': page < total_pages,
                        'has_prev': page > 1
                    }
                },
                'message': 'تم جلب المنتجات بنجاح'
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'خطأ في جلب المنتجات',
            'message': str(e)
        }), 500

@products_bp.route('/featured', methods=['GET'])
def get_featured_products():
    """Get featured products"""
    try:
        limit = min(int(request.args.get('limit', 8)), 20)

        with get_db_connection() as conn:
            cursor = conn.cursor()

            query = """
                SELECT
                    p.id, p.name_ar, p.name_en, p.description_ar, p.description_en,
                    p.price, p.sale_price, p.sku, p.stock_quantity, p.images,
                    p.is_featured, p.created_at,
                    c.id as category_id, c.name_ar as category_name_ar, c.name_en as category_name_en
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE p.is_active = 1 AND p.is_featured = 1
                ORDER BY p.created_at DESC
                LIMIT ?
            """

            cursor.execute(query, [limit])
            products = cursor.fetchall()

            # Format products
            formatted_products = []
            for product in products:
                product_dict = dict(product)
                # Parse images JSON
                try:
                    product_dict['images'] = json.loads(product_dict['images']) if product_dict['images'] else []
                except:
                    product_dict['images'] = []

                # Add category info
                if product_dict['category_id']:
                    product_dict['category'] = {
                        'id': product_dict['category_id'],
                        'name_ar': product_dict['category_name_ar'],
                        'name_en': product_dict['category_name_en']
                    }
                else:
                    product_dict['category'] = None

                # Remove redundant fields
                del product_dict['category_id']
                del product_dict['category_name_ar']
                del product_dict['category_name_en']

                formatted_products.append(product_dict)

            return jsonify({
                'success': True,
                'data': {
                    'products': formatted_products
                },
                'message': 'تم جلب المنتجات المميزة بنجاح'
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'خطأ في جلب المنتجات المميزة',
            'message': str(e)
        }), 500

@products_bp.route('/<int:product_id>', methods=['GET'])
def get_product(product_id):
    """Get single product by ID"""
    try:
        with get_db_connection() as conn:
            cursor = conn.cursor()

            query = """
                SELECT
                    p.id, p.name_ar, p.name_en, p.description_ar, p.description_en,
                    p.price, p.sale_price, p.sku, p.stock_quantity, p.images,
                    p.is_featured, p.created_at,
                    c.id as category_id, c.name_ar as category_name_ar, c.name_en as category_name_en
                FROM products p
                LEFT JOIN categories c ON p.category_id = c.id
                WHERE p.id = ? AND p.is_active = 1
            """

            cursor.execute(query, [product_id])
            product = cursor.fetchone()

            if not product:
                return jsonify({
                    'success': False,
                    'error': 'المنتج غير موجود',
                    'message': 'Product not found'
                }), 404

            # Format product
            product_dict = dict(product)
            # Parse images JSON
            try:
                product_dict['images'] = json.loads(product_dict['images']) if product_dict['images'] else []
            except:
                product_dict['images'] = []

            # Add category info
            if product_dict['category_id']:
                product_dict['category'] = {
                    'id': product_dict['category_id'],
                    'name_ar': product_dict['category_name_ar'],
                    'name_en': product_dict['category_name_en']
                }
            else:
                product_dict['category'] = None

            # Remove redundant fields
            del product_dict['category_id']
            del product_dict['category_name_ar']
            del product_dict['category_name_en']

            return jsonify({
                'success': True,
                'data': {
                    'product': product_dict
                },
                'message': 'تم جلب المنتج بنجاح'
            })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'خطأ في جلب المنتج',
            'message': str(e)
        }), 500
