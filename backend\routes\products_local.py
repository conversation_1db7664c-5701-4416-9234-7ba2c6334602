"""
Product routes using local database adapter
"""

from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from config.database_adapter import db
import json
import math

products_bp = Blueprint('products', __name__)

@products_bp.route('/', methods=['GET'])
def get_products():
    """Get products with filtering, searching, and pagination"""
    try:
        # Get query parameters
        page = int(request.args.get('page', 1))
        per_page = min(int(request.args.get('per_page', 20)), 50)
        category_id = request.args.get('category_id')
        search = request.args.get('search', '').strip()
        sort_by = request.args.get('sort_by', 'created_at')
        sort_order = request.args.get('sort_order', 'desc')
        min_price = request.args.get('min_price')
        max_price = request.args.get('max_price')
        is_featured = request.args.get('is_featured')
        
        # Build WHERE clause
        where_conditions = ["is_active = 1"]
        params = []
        
        if category_id:
            where_conditions.append("category_id = ?")
            params.append(category_id)
        
        if search:
            where_conditions.append("(name_ar LIKE ? OR name_en LIKE ? OR description_ar LIKE ? OR description_en LIKE ?)")
            search_term = f"%{search}%"
            params.extend([search_term, search_term, search_term, search_term])
        
        if min_price:
            where_conditions.append("price >= ?")
            params.append(float(min_price))
        
        if max_price:
            where_conditions.append("price <= ?")
            params.append(float(max_price))
        
        if is_featured:
            where_conditions.append("is_featured = ?")
            params.append(1 if is_featured.lower() == 'true' else 0)
        
        where_clause = " AND ".join(where_conditions)
        
        # Build ORDER BY clause
        valid_sort_fields = ['name_ar', 'name_en', 'price', 'created_at', 'updated_at']
        if sort_by not in valid_sort_fields:
            sort_by = 'created_at'
        
        sort_order = 'DESC' if sort_order.upper() == 'DESC' else 'ASC'
        order_by = f"{sort_by} {sort_order}"
        
        # Get total count
        count_query = f"SELECT COUNT(*) as count FROM products WHERE {where_clause}"
        with db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute(count_query, params)
            total_count = cursor.fetchone()['count']
        
        # Calculate pagination
        offset = (page - 1) * per_page
        total_pages = math.ceil(total_count / per_page)
        
        # Get products
        products = db.select(
            'products',
            columns='*',
            where=where_clause,
            params=params,
            limit=per_page,
            offset=offset,
            order_by=order_by
        )
        
        # Convert to list of dicts and parse JSON fields
        products_list = []
        for product in products:
            product_dict = dict(product)
            # Parse images JSON field
            try:
                product_dict['images'] = json.loads(product_dict['images'])
            except:
                product_dict['images'] = []
            
            # Convert boolean fields
            product_dict['is_active'] = bool(product_dict['is_active'])
            product_dict['is_featured'] = bool(product_dict['is_featured'])
            
            products_list.append(product_dict)
        
        return jsonify({
            'success': True,
            'data': products_list,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total_count,
                'pages': total_pages,
                'has_next': page < total_pages,
                'has_prev': page > 1
            },
            'message': 'تم جلب المنتجات بنجاح',
            'message_en': 'Products retrieved successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'خطأ في جلب المنتجات',
            'message': str(e)
        }), 500

@products_bp.route('/<product_id>', methods=['GET'])
def get_product(product_id):
    """Get single product by ID"""
    try:
        products = db.select('products', where='id = ? AND is_active = 1', params=[product_id])
        
        if not products:
            return jsonify({
                'success': False,
                'error': 'المنتج غير موجود',
                'message': 'Product not found'
            }), 404
        
        product = dict(products[0])
        
        # Parse images JSON field
        try:
            product['images'] = json.loads(product['images'])
        except:
            product['images'] = []
        
        # Convert boolean fields
        product['is_active'] = bool(product['is_active'])
        product['is_featured'] = bool(product['is_featured'])
        
        # Get category info if exists
        if product['category_id']:
            categories = db.select('categories', where='id = ?', params=[product['category_id']])
            if categories:
                product['category'] = dict(categories[0])
        
        return jsonify({
            'success': True,
            'data': product,
            'message': 'تم جلب المنتج بنجاح',
            'message_en': 'Product retrieved successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'خطأ في جلب المنتج',
            'message': str(e)
        }), 500

@products_bp.route('/featured', methods=['GET'])
def get_featured_products():
    """Get featured products"""
    try:
        limit = min(int(request.args.get('limit', 10)), 20)
        
        products = db.select(
            'products',
            where='is_active = 1 AND is_featured = 1',
            limit=limit,
            order_by='created_at DESC'
        )
        
        products_list = []
        for product in products:
            product_dict = dict(product)
            try:
                product_dict['images'] = json.loads(product_dict['images'])
            except:
                product_dict['images'] = []
            
            product_dict['is_active'] = bool(product_dict['is_active'])
            product_dict['is_featured'] = bool(product_dict['is_featured'])
            
            products_list.append(product_dict)
        
        return jsonify({
            'success': True,
            'data': products_list,
            'message': 'تم جلب المنتجات المميزة بنجاح',
            'message_en': 'Featured products retrieved successfully'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'خطأ في جلب المنتجات المميزة',
            'message': str(e)
        }), 500

@products_bp.route('/search', methods=['GET'])
def search_products():
    """Search products"""
    try:
        query = request.args.get('q', '').strip()
        limit = min(int(request.args.get('limit', 20)), 50)
        
        if not query:
            return jsonify({
                'success': False,
                'error': 'مطلوب كلمة البحث',
                'message': 'Search query is required'
            }), 400
        
        # Search in name and description
        where_clause = "(name_ar LIKE ? OR name_en LIKE ? OR description_ar LIKE ? OR description_en LIKE ?) AND is_active = 1"
        search_term = f"%{query}%"
        params = [search_term, search_term, search_term, search_term]
        
        products = db.select(
            'products',
            where=where_clause,
            params=params,
            limit=limit,
            order_by='is_featured DESC, created_at DESC'
        )
        
        products_list = []
        for product in products:
            product_dict = dict(product)
            try:
                product_dict['images'] = json.loads(product_dict['images'])
            except:
                product_dict['images'] = []
            
            product_dict['is_active'] = bool(product_dict['is_active'])
            product_dict['is_featured'] = bool(product_dict['is_featured'])
            
            products_list.append(product_dict)
        
        return jsonify({
            'success': True,
            'data': products_list,
            'query': query,
            'count': len(products_list),
            'message': f'تم العثور على {len(products_list)} منتج',
            'message_en': f'Found {len(products_list)} products'
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': 'خطأ في البحث',
            'message': str(e)
        }), 500