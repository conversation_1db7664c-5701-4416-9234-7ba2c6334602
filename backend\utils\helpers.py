"""
Helper utilities for the e-commerce application
"""

from flask import jsonify
from typing import Any, Optional, Dict
import uuid
import os
from datetime import datetime
import json

def generate_response(
    success: bool,
    message: str,
    message_en: str = None,
    data: Any = None,
    error: str = None,
    status_code: int = None
) -> Dict:
    """Generate standardized API response"""
    response = {
        'success': success,
        'message': message,
        'timestamp': datetime.utcnow().isoformat()
    }
    
    if message_en:
        response['message_en'] = message_en
    
    if data is not None:
        response['data'] = data
    
    if error and not success:
        response['error'] = error
    
    return response

def generate_uuid() -> str:
    """Generate a new UUID"""
    return str(uuid.uuid4())

def format_price(price: float, currency: str = 'SAR') -> str:
    """Format price with currency"""
    return f"{price:.2f} {currency}"

def calculate_tax(amount: float, tax_rate: float = 0.15) -> float:
    """Calculate tax amount (default 15% VAT for Saudi Arabia)"""
    return round(amount * tax_rate, 2)

def calculate_discount(original_price: float, discount_percentage: float) -> float:
    """Calculate discount amount"""
    return round(original_price * (discount_percentage / 100), 2)

def format_order_number(order_id: str) -> str:
    """Format order number"""
    # Take first 8 characters of UUID and add timestamp
    timestamp = datetime.now().strftime('%Y%m%d')
    return f"ORD-{timestamp}-{order_id[:8].upper()}"

def validate_image_file(filename: str) -> bool:
    """Validate image file extension"""
    allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in allowed_extensions

def get_file_extension(filename: str) -> str:
    """Get file extension"""
    return filename.rsplit('.', 1)[1].lower() if '.' in filename else ''

def generate_filename(original_filename: str, prefix: str = '') -> str:
    """Generate unique filename"""
    extension = get_file_extension(original_filename)
    unique_id = generate_uuid()[:8]
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    if prefix:
        return f"{prefix}_{timestamp}_{unique_id}.{extension}"
    return f"{timestamp}_{unique_id}.{extension}"

def safe_json_loads(json_string: str, default: Any = None) -> Any:
    """Safely parse JSON string"""
    try:
        return json.loads(json_string) if json_string else default
    except (json.JSONDecodeError, TypeError):
        return default

def safe_json_dumps(data: Any) -> str:
    """Safely convert to JSON string"""
    try:
        return json.dumps(data, ensure_ascii=False)
    except (TypeError, ValueError):
        return '{}'

def paginate_query(query, page: int = 1, per_page: int = 20):
    """Add pagination to Supabase query"""
    offset = (page - 1) * per_page
    return query.range(offset, offset + per_page - 1)

def build_search_query(search_term: str, fields: list) -> str:
    """Build search query for multiple fields"""
    if not search_term or not fields:
        return ""
    
    # Create OR conditions for each field
    conditions = []
    for field in fields:
        conditions.append(f"{field}.ilike.%{search_term}%")
    
    return f"or({','.join(conditions)})"

def format_phone_number(phone: str) -> str:
    """Format phone number to Saudi standard"""
    if not phone:
        return ""
    
    # Remove all non-digit characters except +
    clean_phone = ''.join(c for c in phone if c.isdigit() or c == '+')
    
    # Convert to international format
    if clean_phone.startswith('05'):
        return f"+966{clean_phone[1:]}"
    elif clean_phone.startswith('5') and len(clean_phone) == 9:
        return f"+966{clean_phone}"
    elif clean_phone.startswith('966'):
        return f"+{clean_phone}"
    elif clean_phone.startswith('+966'):
        return clean_phone
    
    return phone  # Return original if can't format

def calculate_shipping_cost(total_amount: float, weight: float = 0) -> float:
    """Calculate shipping cost based on order total and weight"""
    # Free shipping for orders over 200 SAR
    if total_amount >= 200:
        return 0.0
    
    # Base shipping cost
    base_cost = 25.0
    
    # Additional cost for heavy items (over 5kg)
    if weight > 5:
        additional_cost = (weight - 5) * 5  # 5 SAR per additional kg
        return base_cost + additional_cost
    
    return base_cost

def get_order_status_ar(status: str) -> str:
    """Get Arabic translation for order status"""
    status_translations = {
        'pending': 'في الانتظار',
        'confirmed': 'مؤكد',
        'processing': 'قيد المعالجة',
        'shipped': 'تم الشحن',
        'delivered': 'تم التسليم',
        'cancelled': 'ملغي',
        'refunded': 'مسترد'
    }
    return status_translations.get(status, status)

def get_payment_status_ar(status: str) -> str:
    """Get Arabic translation for payment status"""
    status_translations = {
        'pending': 'في الانتظار',
        'paid': 'مدفوع',
        'failed': 'فشل',
        'refunded': 'مسترد'
    }
    return status_translations.get(status, status)

def clean_html_tags(text: str) -> str:
    """Remove HTML tags from text"""
    import re
    if not text:
        return ""
    
    # Remove HTML tags
    clean = re.compile('<.*?>')
    return re.sub(clean, '', text)

def truncate_text(text: str, max_length: int = 100, suffix: str = '...') -> str:
    """Truncate text to specified length"""
    if not text or len(text) <= max_length:
        return text
    
    return text[:max_length - len(suffix)] + suffix

def is_valid_uuid(uuid_string: str) -> bool:
    """Check if string is a valid UUID"""
    try:
        uuid.UUID(uuid_string)
        return True
    except (ValueError, TypeError):
        return False

def get_client_ip(request) -> str:
    """Get client IP address from request"""
    # Check for forwarded IP first (in case of proxy)
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    else:
        return request.remote_addr or 'unknown'
