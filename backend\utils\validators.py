"""
Validation utilities for the e-commerce application
"""

import re
from typing import Optional

def validate_email(email: str) -> bool:
    """Validate email format"""
    if not email or not isinstance(email, str):
        return False
    
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(email_pattern, email.strip()) is not None

def validate_password(password: str) -> bool:
    """Validate password strength"""
    if not password or not isinstance(password, str):
        return False
    
    # At least 8 characters
    if len(password) < 8:
        return False
    
    # Contains at least one letter and one number
    has_letter = re.search(r'[a-zA-Z]', password)
    has_number = re.search(r'\d', password)
    
    return has_letter is not None and has_number is not None

def validate_phone(phone: str) -> bool:
    """Validate phone number (Saudi format)"""
    if not phone or not isinstance(phone, str):
        return True  # Phone is optional
    
    # Remove spaces and special characters
    clean_phone = re.sub(r'[^\d+]', '', phone)
    
    # Saudi phone number patterns
    patterns = [
        r'^\+966[5][0-9]{8}$',  # +966 5XXXXXXXX
        r'^966[5][0-9]{8}$',    # 966 5XXXXXXXX
        r'^05[0-9]{8}$',        # 05XXXXXXXX
        r'^5[0-9]{8}$'          # 5XXXXXXXX
    ]
    
    return any(re.match(pattern, clean_phone) for pattern in patterns)

def validate_price(price) -> bool:
    """Validate price value"""
    try:
        price_float = float(price)
        return price_float >= 0
    except (ValueError, TypeError):
        return False

def validate_quantity(quantity) -> bool:
    """Validate quantity value"""
    try:
        quantity_int = int(quantity)
        return quantity_int > 0
    except (ValueError, TypeError):
        return False

def validate_sku(sku: str) -> bool:
    """Validate SKU format"""
    if not sku or not isinstance(sku, str):
        return False
    
    # SKU should be alphanumeric with hyphens, 3-50 characters
    sku_pattern = r'^[A-Z0-9\-]{3,50}$'
    return re.match(sku_pattern, sku.upper()) is not None

def validate_rating(rating) -> bool:
    """Validate rating value (1-5)"""
    try:
        rating_int = int(rating)
        return 1 <= rating_int <= 5
    except (ValueError, TypeError):
        return False

def validate_order_status(status: str) -> bool:
    """Validate order status"""
    valid_statuses = ['pending', 'confirmed', 'processing', 'shipped', 'delivered', 'cancelled', 'refunded']
    return status in valid_statuses

def validate_payment_status(status: str) -> bool:
    """Validate payment status"""
    valid_statuses = ['pending', 'paid', 'failed', 'refunded']
    return status in valid_statuses

def validate_coupon_code(code: str) -> bool:
    """Validate coupon code format"""
    if not code or not isinstance(code, str):
        return False
    
    # Coupon code should be alphanumeric, 3-20 characters
    code_pattern = r'^[A-Z0-9]{3,20}$'
    return re.match(code_pattern, code.upper()) is not None

def validate_address_data(address_data: dict) -> tuple[bool, Optional[str]]:
    """Validate address data"""
    required_fields = ['first_name', 'last_name', 'address_line_1', 'city', 'country']
    
    for field in required_fields:
        if not address_data.get(field, '').strip():
            return False, f'حقل {field} مطلوب'
    
    # Validate phone if provided
    if address_data.get('phone') and not validate_phone(address_data['phone']):
        return False, 'رقم الهاتف غير صحيح'
    
    return True, None

def validate_product_data(product_data: dict) -> tuple[bool, Optional[str]]:
    """Validate product data"""
    required_fields = ['name_ar', 'price', 'category_id']
    
    for field in required_fields:
        if not product_data.get(field):
            return False, f'حقل {field} مطلوب'
    
    # Validate price
    if not validate_price(product_data['price']):
        return False, 'السعر غير صحيح'
    
    # Validate sale price if provided
    if product_data.get('sale_price') and not validate_price(product_data['sale_price']):
        return False, 'سعر التخفيض غير صحيح'
    
    # Validate stock quantity
    if product_data.get('stock_quantity') is not None and not validate_quantity(product_data['stock_quantity']):
        return False, 'كمية المخزون غير صحيحة'
    
    # Validate SKU if provided
    if product_data.get('sku') and not validate_sku(product_data['sku']):
        return False, 'رمز المنتج غير صحيح'
    
    return True, None

def sanitize_string(text: str, max_length: int = None) -> str:
    """Sanitize string input"""
    if not text or not isinstance(text, str):
        return ''
    
    # Remove leading/trailing whitespace
    sanitized = text.strip()
    
    # Limit length if specified
    if max_length and len(sanitized) > max_length:
        sanitized = sanitized[:max_length]
    
    return sanitized

def validate_search_query(query: str) -> bool:
    """Validate search query"""
    if not query or not isinstance(query, str):
        return False
    
    # Query should be at least 2 characters and not more than 100
    clean_query = query.strip()
    return 2 <= len(clean_query) <= 100
