-- Sample data for the e-commerce database
-- This file contains sample data to populate the database for testing

-- Insert sample categories
INSERT INTO categories (id, name_ar, name_en, description_ar, description_en, image_url, is_active, sort_order) VALUES
('550e8400-e29b-41d4-a716-446655440001', 'الإلكترونيات', 'Electronics', 'أجهزة إلكترونية متنوعة', 'Various electronic devices', 'https://images.unsplash.com/photo-1498049794561-7780e7231661?w=400', true, 1),
('550e8400-e29b-41d4-a716-446655440002', 'الملابس', 'Clothing', 'ملابس رجالية ونسائية', 'Men and women clothing', 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400', true, 2),
('550e8400-e29b-41d4-a716-446655440003', 'المنزل والحديقة', 'Home & Garden', 'أدوات منزلية ومستلزمات الحديقة', 'Home tools and garden supplies', 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=400', true, 3),
('550e8400-e29b-41d4-a716-446655440004', 'الكتب', 'Books', 'كتب متنوعة في جميع المجالات', 'Various books in all fields', 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400', true, 4),
('550e8400-e29b-41d4-a716-446655440005', 'الرياضة واللياقة', 'Sports & Fitness', 'معدات رياضية ولياقة بدنية', 'Sports and fitness equipment', 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400', true, 5);

-- Insert subcategories
INSERT INTO categories (id, name_ar, name_en, description_ar, description_en, parent_id, is_active, sort_order) VALUES
('550e8400-e29b-41d4-a716-446655440011', 'الهواتف الذكية', 'Smartphones', 'هواتف ذكية حديثة', 'Modern smartphones', '550e8400-e29b-41d4-a716-446655440001', true, 1),
('550e8400-e29b-41d4-a716-446655440012', 'أجهزة الكمبيوتر', 'Computers', 'أجهزة كمبيوتر محمولة ومكتبية', 'Laptops and desktop computers', '550e8400-e29b-41d4-a716-446655440001', true, 2),
('550e8400-e29b-41d4-a716-446655440021', 'ملابس رجالية', 'Men\'s Clothing', 'ملابس للرجال', 'Clothing for men', '550e8400-e29b-41d4-a716-446655440002', true, 1),
('550e8400-e29b-41d4-a716-446655440022', 'ملابس نسائية', 'Women\'s Clothing', 'ملابس للنساء', 'Clothing for women', '550e8400-e29b-41d4-a716-446655440002', true, 2);

-- Insert sample products
INSERT INTO products (id, name_ar, name_en, description_ar, description_en, short_description_ar, short_description_en, price, sale_price, sku, stock_quantity, category_id, images, is_active, is_featured) VALUES
('650e8400-e29b-41d4-a716-446655440001', 'آيفون 15 برو', 'iPhone 15 Pro', 'هاتف آيفون 15 برو الجديد بتقنيات متطورة وكاميرا احترافية', 'New iPhone 15 Pro with advanced technology and professional camera', 'آيفون 15 برو - أحدث إصدار', 'iPhone 15 Pro - Latest version', 4999.00, 4499.00, 'IP15PRO-128', 50, '550e8400-e29b-41d4-a716-446655440011', '["https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400", "https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?w=400"]', true, true),

('650e8400-e29b-41d4-a716-446655440002', 'لابتوب ديل XPS 13', 'Dell XPS 13 Laptop', 'لابتوب ديل XPS 13 بمعالج إنتل كور i7 وذاكرة 16 جيجا', 'Dell XPS 13 laptop with Intel Core i7 processor and 16GB RAM', 'لابتوب عالي الأداء', 'High performance laptop', 6999.00, null, 'DELL-XPS13-I7', 25, '550e8400-e29b-41d4-a716-446655440012', '["https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400", "https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400"]', true, true),

('650e8400-e29b-41d4-a716-446655440003', 'قميص قطني رجالي', 'Men\'s Cotton Shirt', 'قميص قطني عالي الجودة للرجال بألوان متعددة', 'High quality cotton shirt for men in multiple colors', 'قميص قطني مريح', 'Comfortable cotton shirt', 199.00, 149.00, 'SHIRT-MEN-COT', 100, '550e8400-e29b-41d4-a716-446655440021', '["https://images.unsplash.com/photo-1602810318383-e386cc2a3ccf?w=400", "https://images.unsplash.com/photo-1596755094514-f87e34085b2c?w=400"]', true, false),

('650e8400-e29b-41d4-a716-446655440004', 'فستان صيفي نسائي', 'Women\'s Summer Dress', 'فستان صيفي أنيق للنساء بتصميم عصري', 'Elegant summer dress for women with modern design', 'فستان صيفي أنيق', 'Elegant summer dress', 299.00, null, 'DRESS-WOM-SUM', 75, '550e8400-e29b-41d4-a716-446655440022', '["https://images.unsplash.com/photo-1595777457583-95e059d581b8?w=400", "https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=400"]', true, false),

('650e8400-e29b-41d4-a716-446655440005', 'كتاب البرمجة بـ Python', 'Python Programming Book', 'كتاب شامل لتعلم البرمجة بلغة Python من الصفر', 'Comprehensive book for learning Python programming from scratch', 'تعلم البرمجة بسهولة', 'Learn programming easily', 89.00, 69.00, 'BOOK-PYTHON-AR', 200, '550e8400-e29b-41d4-a716-446655440004', '["https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?w=400"]', true, true),

('650e8400-e29b-41d4-a716-446655440006', 'دمبل قابل للتعديل', 'Adjustable Dumbbell', 'دمبل قابل للتعديل من 5 إلى 25 كيلو', 'Adjustable dumbbell from 5 to 25 kg', 'معدات رياضية منزلية', 'Home fitness equipment', 599.00, 499.00, 'DUMBBELL-ADJ-25', 30, '550e8400-e29b-41d4-a716-446655440005', '["https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400", "https://images.unsplash.com/photo-1534438327276-14e5300c3a48?w=400"]', true, false);

-- Insert sample coupons
INSERT INTO coupons (id, code, name_ar, name_en, description_ar, description_en, type, value, minimum_amount, usage_limit, is_active, starts_at, expires_at) VALUES
('750e8400-e29b-41d4-a716-446655440001', 'WELCOME10', 'خصم الترحيب', 'Welcome Discount', 'خصم 10% للعملاء الجدد', '10% discount for new customers', 'percentage', 10.00, 100.00, 1000, true, NOW(), NOW() + INTERVAL '30 days'),
('750e8400-e29b-41d4-a716-446655440002', 'SAVE50', 'خصم 50 ريال', '50 SAR Discount', 'خصم 50 ريال على الطلبات أكثر من 500 ريال', '50 SAR discount on orders over 500 SAR', 'fixed', 50.00, 500.00, 500, true, NOW(), NOW() + INTERVAL '60 days'),
('750e8400-e29b-41d4-a716-446655440003', 'ELECTRONICS15', 'خصم الإلكترونيات', 'Electronics Discount', 'خصم 15% على جميع الأجهزة الإلكترونية', '15% discount on all electronics', 'percentage', 15.00, 200.00, 200, true, NOW(), NOW() + INTERVAL '15 days');

-- Function to generate order number
CREATE OR REPLACE FUNCTION generate_order_number()
RETURNS TEXT AS $$
DECLARE
    new_number TEXT;
    counter INTEGER;
BEGIN
    -- Get the current date in YYYYMMDD format
    SELECT TO_CHAR(NOW(), 'YYYYMMDD') INTO new_number;
    
    -- Get the count of orders created today
    SELECT COUNT(*) + 1 INTO counter
    FROM orders 
    WHERE DATE(created_at) = CURRENT_DATE;
    
    -- Combine date with counter (padded to 4 digits)
    new_number := new_number || LPAD(counter::TEXT, 4, '0');
    
    RETURN new_number;
END;
$$ LANGUAGE plpgsql;

-- Create a trigger to auto-generate order numbers
CREATE OR REPLACE FUNCTION set_order_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.order_number IS NULL OR NEW.order_number = '' THEN
        NEW.order_number := generate_order_number();
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_set_order_number
    BEFORE INSERT ON orders
    FOR EACH ROW
    EXECUTE FUNCTION set_order_number();
