-- إعد<PERSON> قاعدة البيانات للمتجر الإلكتروني
-- Database setup for e-commerce store

-- إنشاء جدول الفئات (Categories)
CREATE TABLE categories (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255) NOT NULL,
    description_ar TEXT,
    description_en TEXT,
    image_url VARCHAR(500),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إن<PERSON><PERSON><PERSON> جدول المنتجات (Products)
CREATE TABLE products (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    name_ar VARCHAR(255) NOT NULL,
    name_en VARCHAR(255) NOT NULL,
    description_ar TEXT,
    description_en TEXT,
    price DECIMAL(10,2) NOT NULL,
    sale_price DECIMAL(10,2),
    sku VARCHAR(100) UNIQUE NOT NULL,
    stock_quantity INTEGER DEFAULT 0,
    images JSONB DEFAULT '[]',
    category_id UUID REFERENCES categories(id),
    is_featured BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول المستخدمين (Users)
CREATE TABLE users (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    phone VARCHAR(20),
    address TEXT,
    city VARCHAR(100),
    postal_code VARCHAR(20),
    is_admin BOOLEAN DEFAULT false,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول عربة التسوق (Cart Items)
CREATE TABLE cart_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL DEFAULT 1,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, product_id)
);

-- إنشاء جدول الطلبات (Orders)
CREATE TABLE orders (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    total_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    shipping_address TEXT NOT NULL,
    payment_method VARCHAR(50),
    payment_status VARCHAR(50) DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إنشاء جدول عناصر الطلبات (Order Items)
CREATE TABLE order_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    order_id UUID REFERENCES orders(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id),
    quantity INTEGER NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- إدراج بيانات تجريبية للفئات
INSERT INTO categories (name_ar, name_en, description_ar, description_en, image_url) VALUES
('إلكترونيات', 'Electronics', 'أجهزة إلكترونية متنوعة', 'Various electronic devices', 'https://via.placeholder.com/400x300?text=Electronics'),
('ملابس', 'Clothing', 'ملابس رجالية ونسائية', 'Men and women clothing', 'https://via.placeholder.com/400x300?text=Clothing'),
('منزل وحديقة', 'Home & Garden', 'أدوات منزلية', 'Home tools', 'https://via.placeholder.com/400x300?text=Home'),
('رياضة', 'Sports', 'معدات رياضية', 'Sports equipment', 'https://via.placeholder.com/400x300?text=Sports'),
('كتب', 'Books', 'كتب متنوعة', 'Various books', 'https://via.placeholder.com/400x300?text=Books');

-- إدراج بيانات تجريبية للمنتجات
INSERT INTO products (name_ar, name_en, description_ar, description_en, price, sale_price, sku, stock_quantity, images, category_id, is_featured) VALUES
('هاتف iPhone 15', 'iPhone 15', 'أحدث هاتف من Apple مع ميزات متطورة', 'Latest iPhone from Apple with advanced features', 3999.99, 3699.99, 'IPHONE-15', 15, '["https://via.placeholder.com/400x400?text=iPhone+15", "https://via.placeholder.com/400x400?text=iPhone+15+Pro"]', (SELECT id FROM categories WHERE name_en = 'Electronics'), true),
('لابتوب Dell XPS', 'Dell XPS Laptop', 'لابتوب Dell XPS عالي الأداء مع معالج Intel Core i7', 'High performance Dell XPS laptop with Intel Core i7', 2999.99, 2699.99, 'DELL-XPS001', 10, '["https://via.placeholder.com/400x400?text=Dell+XPS", "https://via.placeholder.com/400x400?text=Dell+XPS+2"]', (SELECT id FROM categories WHERE name_en = 'Electronics'), true),
('قميص قطني', 'Cotton Shirt', 'قميص قطني عالي الجودة', 'High quality cotton shirt', 89.99, 69.99, 'SHIRT-001', 50, '["https://via.placeholder.com/400x400?text=Cotton+Shirt"]', (SELECT id FROM categories WHERE name_en = 'Clothing'), false),
('كتاب البرمجة', 'Programming Book', 'كتاب تعلم البرمجة للمبتدئين', 'Programming book for beginners', 45.00, NULL, 'BOOK-PROG001', 25, '["https://via.placeholder.com/400x400?text=Programming+Book"]', (SELECT id FROM categories WHERE name_en = 'Books'), true);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX idx_products_category_id ON products(category_id);
CREATE INDEX idx_products_is_featured ON products(is_featured);
CREATE INDEX idx_products_is_active ON products(is_active);
CREATE INDEX idx_cart_items_user_id ON cart_items(user_id);
CREATE INDEX idx_orders_user_id ON orders(user_id);
CREATE INDEX idx_order_items_order_id ON order_items(order_id);

-- إعداد Row Level Security (RLS)
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE cart_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;

-- سياسات الأمان للقراءة العامة للفئات والمنتجات
CREATE POLICY "Allow public read access to categories" ON categories FOR SELECT USING (is_active = true);
CREATE POLICY "Allow public read access to products" ON products FOR SELECT USING (is_active = true);

-- سياسات الأمان للمستخدمين المسجلين
CREATE POLICY "Users can view their own data" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update their own data" ON users FOR UPDATE USING (auth.uid() = id);

-- سياسات الأمان لعربة التسوق
CREATE POLICY "Users can manage their own cart" ON cart_items FOR ALL USING (auth.uid() = user_id);

-- سياسات الأمان للطلبات
CREATE POLICY "Users can view their own orders" ON orders FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create their own orders" ON orders FOR INSERT WITH CHECK (auth.uid() = user_id);
