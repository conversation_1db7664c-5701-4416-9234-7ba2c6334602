import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useCart } from '../../contexts/CartContext';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const { user, signOut, isAuthenticated } = useAuth();
  const { itemCount } = useCart();
  const navigate = useNavigate();

  const handleSignOut = async () => {
    await signOut();
    navigate('/');
    setIsUserMenuOpen(false);
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const toggleUserMenu = () => {
    setIsUserMenuOpen(!isUserMenuOpen);
  };

  return (
    <header className="header">
      <div className="container">
        <div className="header-content">
          {/* Logo */}
          <div className="logo">
            <Link to="/" className="logo-link">
              <h1>متجر شغف</h1>
            </Link>
          </div>

          {/* Navigation */}
          <nav className={`nav ${isMenuOpen ? 'nav-open' : ''}`}>
            <ul className="nav-list">
              <li><Link to="/" className="nav-link">الرئيسية</Link></li>
              <li><Link to="/products" className="nav-link">المنتجات</Link></li>
              <li><Link to="/categories" className="nav-link">الفئات</Link></li>
              <li><Link to="/about" className="nav-link">من نحن</Link></li>
              <li><Link to="/contact" className="nav-link">اتصل بنا</Link></li>
            </ul>
          </nav>

          {/* User Actions */}
          <div className="header-actions">
            {/* Search */}
            <div className="search-container">
              <input
                type="text"
                placeholder="ابحث عن المنتجات..."
                className="search-input"
              />
              <button className="search-btn">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <circle cx="11" cy="11" r="8"></circle>
                  <path d="m21 21-4.35-4.35"></path>
                </svg>
              </button>
            </div>

            {/* Cart */}
            <Link to="/cart" className="cart-link">
              <div className="cart-icon">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M9 22C9.55228 22 10 21.5523 10 21C10 20.4477 9.55228 20 9 20C8.44772 20 8 20.4477 8 21C8 21.5523 8.44772 22 9 22Z"></path>
                  <path d="M20 22C20.5523 22 21 21.5523 21 21C21 20.4477 20.5523 20 20 20C19.4477 20 19 20.4477 19 21C19 21.5523 19.4477 22 20 22Z"></path>
                  <path d="M1 1H5L7.68 14.39C7.77144 14.8504 8.02191 15.264 8.38755 15.5583C8.75318 15.8526 9.2107 16.009 9.68 16H19.4C19.8693 16.009 20.3268 15.8526 20.6925 15.5583C21.0581 15.264 21.3086 14.8504 21.4 14.39L23 6H6"></path>
                </svg>
                {itemCount > 0 && <span className="cart-count">{itemCount}</span>}
              </div>
            </Link>

            {/* User Menu */}
            {isAuthenticated ? (
              <div className="user-menu">
                <button onClick={toggleUserMenu} className="user-menu-btn">
                  <div className="user-avatar">
                    {user?.user_metadata?.first_name?.charAt(0) || user?.email?.charAt(0) || 'U'}
                  </div>
                </button>
                {isUserMenuOpen && (
                  <div className="user-dropdown">
                    <Link to="/profile" className="dropdown-item" onClick={() => setIsUserMenuOpen(false)}>
                      الملف الشخصي
                    </Link>
                    <Link to="/orders" className="dropdown-item" onClick={() => setIsUserMenuOpen(false)}>
                      طلباتي
                    </Link>
                    <Link to="/addresses" className="dropdown-item" onClick={() => setIsUserMenuOpen(false)}>
                      العناوين
                    </Link>
                    <hr className="dropdown-divider" />
                    <button onClick={handleSignOut} className="dropdown-item logout-btn">
                      تسجيل الخروج
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="auth-links">
                <Link to="/login" className="auth-link">تسجيل الدخول</Link>
                <Link to="/register" className="auth-link register">إنشاء حساب</Link>
              </div>
            )}

            {/* Mobile Menu Toggle */}
            <button onClick={toggleMenu} className="mobile-menu-btn">
              <span></span>
              <span></span>
              <span></span>
            </button>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;
