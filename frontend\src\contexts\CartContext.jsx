import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from './AuthContext';
import api from '../services/api';

const CartContext = createContext({});

export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

export const CartProvider = ({ children }) => {
  const [cartItems, setCartItems] = useState([]);
  const [cartSummary, setCartSummary] = useState({
    subtotal: 0,
    tax_amount: 0,
    shipping_cost: 0,
    total_amount: 0,
    item_count: 0
  });
  const [loading, setLoading] = useState(false);
  const { isAuthenticated, token } = useAuth();

  // Load cart when user is authenticated
  useEffect(() => {
    if (isAuthenticated && token) {
      loadCart();
    } else {
      // Clear cart when user logs out
      setCartItems([]);
      setCartSummary({
        subtotal: 0,
        tax_amount: 0,
        shipping_cost: 0,
        total_amount: 0,
        item_count: 0
      });
    }
  }, [isAuthenticated, token]);

  const loadCart = async () => {
    if (!isAuthenticated) return;
    
    try {
      setLoading(true);
      const response = await api.get('/cart');
      if (response.data.success) {
        setCartItems(response.data.data.cart_items);
        setCartSummary(response.data.data.summary);
      }
    } catch (error) {
      console.error('Error loading cart:', error);
    } finally {
      setLoading(false);
    }
  };

  const addToCart = async (productId, quantity = 1) => {
    if (!isAuthenticated) {
      throw new Error('يجب تسجيل الدخول أولاً');
    }

    try {
      setLoading(true);
      const response = await api.post('/cart/add', {
        product_id: productId,
        quantity
      });

      if (response.data.success) {
        await loadCart(); // Reload cart to get updated data
        return { success: true, message: response.data.message };
      } else {
        return { success: false, message: response.data.message };
      }
    } catch (error) {
      const message = error.response?.data?.message || 'خطأ في إضافة المنتج للسلة';
      return { success: false, message };
    } finally {
      setLoading(false);
    }
  };

  const updateCartItem = async (cartItemId, quantity) => {
    if (!isAuthenticated) return;

    try {
      setLoading(true);
      const response = await api.put(`/cart/update/${cartItemId}`, {
        quantity
      });

      if (response.data.success) {
        await loadCart();
        return { success: true, message: response.data.message };
      } else {
        return { success: false, message: response.data.message };
      }
    } catch (error) {
      const message = error.response?.data?.message || 'خطأ في تحديث الكمية';
      return { success: false, message };
    } finally {
      setLoading(false);
    }
  };

  const removeFromCart = async (cartItemId) => {
    if (!isAuthenticated) return;

    try {
      setLoading(true);
      const response = await api.delete(`/cart/remove/${cartItemId}`);

      if (response.data.success) {
        await loadCart();
        return { success: true, message: response.data.message };
      } else {
        return { success: false, message: response.data.message };
      }
    } catch (error) {
      const message = error.response?.data?.message || 'خطأ في حذف المنتج';
      return { success: false, message };
    } finally {
      setLoading(false);
    }
  };

  const clearCart = async () => {
    if (!isAuthenticated) return;

    try {
      setLoading(true);
      const response = await api.delete('/cart/clear');

      if (response.data.success) {
        setCartItems([]);
        setCartSummary({
          subtotal: 0,
          tax_amount: 0,
          shipping_cost: 0,
          total_amount: 0,
          item_count: 0
        });
        return { success: true, message: response.data.message };
      } else {
        return { success: false, message: response.data.message };
      }
    } catch (error) {
      const message = error.response?.data?.message || 'خطأ في تفريغ السلة';
      return { success: false, message };
    } finally {
      setLoading(false);
    }
  };

  const getCartItemCount = () => {
    return cartItems.reduce((total, item) => total + item.quantity, 0);
  };

  const isInCart = (productId) => {
    return cartItems.some(item => item.products?.id === productId);
  };

  const getCartItem = (productId) => {
    return cartItems.find(item => item.products?.id === productId);
  };

  const value = {
    cartItems,
    cartSummary,
    loading,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    loadCart,
    getCartItemCount,
    isInCart,
    getCartItem,
    itemCount: getCartItemCount()
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};
