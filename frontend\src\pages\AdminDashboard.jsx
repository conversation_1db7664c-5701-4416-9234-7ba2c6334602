import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { adminAPI } from '../services/api';

const AdminDashboard = () => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [stats, setStats] = useState({});
  const [products, setProducts] = useState([]);
  const [orders, setOrders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  
  const { user } = useAuth();

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      const response = await adminAPI.getDashboardStats();
      
      if (response.data.success) {
        setStats(response.data.data);
      }
    } catch (error) {
      setError('خطأ في تحميل بيانات لوحة التحكم');
    } finally {
      setLoading(false);
    }
  };

  const loadProducts = async () => {
    try {
      const response = await adminAPI.getProducts();
      if (response.data.success) {
        setProducts(response.data.data.products);
      }
    } catch (error) {
      setError('خطأ في تحميل المنتجات');
    }
  };

  const loadOrders = async () => {
    try {
      const response = await adminAPI.getOrders();
      if (response.data.success) {
        setOrders(response.data.data.orders);
      }
    } catch (error) {
      setError('خطأ في تحميل الطلبات');
    }
  };

  useEffect(() => {
    if (activeTab === 'products') {
      loadProducts();
    } else if (activeTab === 'orders') {
      loadOrders();
    }
  }, [activeTab]);

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>جاري تحميل لوحة التحكم...</p>
      </div>
    );
  }

  return (
    <div className="admin-dashboard">
      <div className="container">
        <div className="admin-header">
          <h1 className="admin-title">لوحة التحكم الإدارية</h1>
          <p className="admin-subtitle">مرحباً {user?.user_metadata?.first_name || 'المدير'}</p>
        </div>

        {error && (
          <div className="error-message">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="15" y1="9" x2="9" y2="15"></line>
              <line x1="9" y1="9" x2="15" y2="15"></line>
            </svg>
            {error}
          </div>
        )}

        <div className="admin-layout">
          {/* Sidebar Navigation */}
          <aside className="admin-sidebar">
            <nav className="admin-nav">
              <button
                className={`nav-item ${activeTab === 'dashboard' ? 'active' : ''}`}
                onClick={() => setActiveTab('dashboard')}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <rect x="3" y="3" width="7" height="9"></rect>
                  <rect x="14" y="3" width="7" height="5"></rect>
                  <rect x="14" y="12" width="7" height="9"></rect>
                  <rect x="3" y="16" width="7" height="5"></rect>
                </svg>
                لوحة التحكم
              </button>
              
              <button
                className={`nav-item ${activeTab === 'products' ? 'active' : ''}`}
                onClick={() => setActiveTab('products')}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path>
                  <line x1="3" y1="6" x2="21" y2="6"></line>
                  <path d="M16 10a4 4 0 0 1-8 0"></path>
                </svg>
                المنتجات
              </button>
              
              <button
                className={`nav-item ${activeTab === 'orders' ? 'active' : ''}`}
                onClick={() => setActiveTab('orders')}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"></path>
                  <rect x="8" y="2" width="8" height="4" rx="1" ry="1"></rect>
                </svg>
                الطلبات
              </button>
              
              <button
                className={`nav-item ${activeTab === 'users' ? 'active' : ''}`}
                onClick={() => setActiveTab('users')}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                  <circle cx="9" cy="7" r="4"></circle>
                  <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                  <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                </svg>
                المستخدمون
              </button>
              
              <button
                className={`nav-item ${activeTab === 'categories' ? 'active' : ''}`}
                onClick={() => setActiveTab('categories')}
              >
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <rect x="3" y="3" width="7" height="7"></rect>
                  <rect x="14" y="3" width="7" height="7"></rect>
                  <rect x="14" y="14" width="7" height="7"></rect>
                  <rect x="3" y="14" width="7" height="7"></rect>
                </svg>
                الفئات
              </button>
            </nav>
          </aside>

          {/* Main Content */}
          <main className="admin-main">
            {/* Dashboard Tab */}
            {activeTab === 'dashboard' && (
              <div className="dashboard-content">
                <div className="stats-grid">
                  <div className="stat-card">
                    <div className="stat-icon">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <circle cx="9" cy="21" r="1"></circle>
                        <circle cx="20" cy="21" r="1"></circle>
                        <path d="m1 1 4 4 2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                      </svg>
                    </div>
                    <div className="stat-info">
                      <div className="stat-value">{stats.total_orders || 0}</div>
                      <div className="stat-label">إجمالي الطلبات</div>
                    </div>
                  </div>

                  <div className="stat-card">
                    <div className="stat-icon">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M6 2L3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4z"></path>
                        <line x1="3" y1="6" x2="21" y2="6"></line>
                        <path d="M16 10a4 4 0 0 1-8 0"></path>
                      </svg>
                    </div>
                    <div className="stat-info">
                      <div className="stat-value">{stats.total_products || 0}</div>
                      <div className="stat-label">إجمالي المنتجات</div>
                    </div>
                  </div>

                  <div className="stat-card">
                    <div className="stat-icon">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                        <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                      </svg>
                    </div>
                    <div className="stat-info">
                      <div className="stat-value">{stats.total_users || 0}</div>
                      <div className="stat-label">إجمالي المستخدمين</div>
                    </div>
                  </div>

                  <div className="stat-card">
                    <div className="stat-icon">
                      <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                        <line x1="12" y1="1" x2="12" y2="23"></line>
                        <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                      </svg>
                    </div>
                    <div className="stat-info">
                      <div className="stat-value">{stats.total_revenue || 0} ريال</div>
                      <div className="stat-label">إجمالي المبيعات</div>
                    </div>
                  </div>
                </div>

                <div className="dashboard-charts">
                  <div className="chart-card">
                    <h3>الطلبات الأخيرة</h3>
                    <div className="recent-orders">
                      {stats.recent_orders?.map(order => (
                        <div key={order.id} className="order-item">
                          <div className="order-info">
                            <div className="order-id">طلب #{order.id}</div>
                            <div className="order-customer">{order.customer_name}</div>
                          </div>
                          <div className="order-details">
                            <div className="order-total">{order.total} ريال</div>
                            <div className={`order-status ${order.status}`}>
                              {order.status === 'pending' && 'قيد الانتظار'}
                              {order.status === 'processing' && 'قيد المعالجة'}
                              {order.status === 'shipped' && 'تم الشحن'}
                              {order.status === 'delivered' && 'تم التسليم'}
                              {order.status === 'cancelled' && 'ملغي'}
                            </div>
                          </div>
                        </div>
                      )) || <p>لا توجد طلبات حديثة</p>}
                    </div>
                  </div>

                  <div className="chart-card">
                    <h3>المنتجات منخفضة المخزون</h3>
                    <div className="low-stock-products">
                      {stats.low_stock_products?.map(product => (
                        <div key={product.id} className="stock-item">
                          <div className="product-info">
                            <div className="product-name">{product.name_ar}</div>
                            <div className="product-sku">رقم المنتج: {product.sku}</div>
                          </div>
                          <div className="stock-quantity">
                            <span className="stock-count">{product.stock_quantity}</span>
                            <span className="stock-label">قطعة</span>
                          </div>
                        </div>
                      )) || <p>جميع المنتجات متوفرة بكمية كافية</p>}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Products Tab */}
            {activeTab === 'products' && (
              <div className="products-content">
                <div className="content-header">
                  <h2>إدارة المنتجات</h2>
                  <button className="btn btn-primary">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <line x1="12" y1="5" x2="12" y2="19"></line>
                      <line x1="5" y1="12" x2="19" y2="12"></line>
                    </svg>
                    إضافة منتج جديد
                  </button>
                </div>

                <div className="data-table">
                  <div className="table-header">
                    <div className="col">المنتج</div>
                    <div className="col">السعر</div>
                    <div className="col">المخزون</div>
                    <div className="col">الحالة</div>
                    <div className="col">إجراءات</div>
                  </div>
                  
                  <div className="table-body">
                    {products.map(product => (
                      <div key={product.id} className="table-row">
                        <div className="col product-col">
                          <img 
                            src={product.images?.[0] || '/images/product-placeholder.jpg'} 
                            alt={product.name_ar}
                            className="product-thumb"
                          />
                          <div className="product-details">
                            <div className="product-name">{product.name_ar}</div>
                            <div className="product-sku">رقم المنتج: {product.sku}</div>
                          </div>
                        </div>
                        <div className="col">
                          {product.sale_price ? (
                            <>
                              <span className="current-price">{product.sale_price} ريال</span>
                              <span className="original-price">{product.price} ريال</span>
                            </>
                          ) : (
                            <span>{product.price} ريال</span>
                          )}
                        </div>
                        <div className="col">
                          <span className={`stock ${product.stock_quantity <= 5 ? 'low' : ''}`}>
                            {product.stock_quantity}
                          </span>
                        </div>
                        <div className="col">
                          <span className={`status ${product.is_active ? 'active' : 'inactive'}`}>
                            {product.is_active ? 'نشط' : 'غير نشط'}
                          </span>
                        </div>
                        <div className="col actions-col">
                          <button className="action-btn edit">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                            </svg>
                          </button>
                          <button className="action-btn delete">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                              <polyline points="3,6 5,6 21,6"></polyline>
                              <path d="m19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"></path>
                            </svg>
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Orders Tab */}
            {activeTab === 'orders' && (
              <div className="orders-content">
                <div className="content-header">
                  <h2>إدارة الطلبات</h2>
                  <div className="filters">
                    <select className="filter-select">
                      <option value="">جميع الطلبات</option>
                      <option value="pending">قيد الانتظار</option>
                      <option value="processing">قيد المعالجة</option>
                      <option value="shipped">تم الشحن</option>
                      <option value="delivered">تم التسليم</option>
                      <option value="cancelled">ملغي</option>
                    </select>
                  </div>
                </div>

                <div className="data-table">
                  <div className="table-header">
                    <div className="col">رقم الطلب</div>
                    <div className="col">العميل</div>
                    <div className="col">التاريخ</div>
                    <div className="col">المجموع</div>
                    <div className="col">الحالة</div>
                    <div className="col">إجراءات</div>
                  </div>
                  
                  <div className="table-body">
                    {orders.map(order => (
                      <div key={order.id} className="table-row">
                        <div className="col">#{order.id}</div>
                        <div className="col">{order.customer_name}</div>
                        <div className="col">{new Date(order.created_at).toLocaleDateString('ar-SA')}</div>
                        <div className="col">{order.total} ريال</div>
                        <div className="col">
                          <span className={`status ${order.status}`}>
                            {order.status === 'pending' && 'قيد الانتظار'}
                            {order.status === 'processing' && 'قيد المعالجة'}
                            {order.status === 'shipped' && 'تم الشحن'}
                            {order.status === 'delivered' && 'تم التسليم'}
                            {order.status === 'cancelled' && 'ملغي'}
                          </span>
                        </div>
                        <div className="col actions-col">
                          <button className="action-btn view">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                              <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                              <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                          </button>
                          <button className="action-btn edit">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                            </svg>
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Other tabs would be implemented similarly */}
            {activeTab === 'users' && (
              <div className="users-content">
                <h2>إدارة المستخدمين</h2>
                <p>قريباً...</p>
              </div>
            )}

            {activeTab === 'categories' && (
              <div className="categories-content">
                <h2>إدارة الفئات</h2>
                <p>قريباً...</p>
              </div>
            )}
          </main>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;
