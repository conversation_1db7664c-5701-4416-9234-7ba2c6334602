import React from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useCart } from '../contexts/CartContext';
import { useAuth } from '../contexts/AuthContext';

const Cart = () => {
  const { 
    cartItems, 
    updateCartItem, 
    removeFromCart, 
    clearCart, 
    cartTotal, 
    cartSubtotal, 
    cartTax, 
    cartShipping,
    loading 
  } = useCart();
  
  const { user } = useAuth();
  const navigate = useNavigate();

  const handleQuantityChange = async (itemId, newQuantity) => {
    if (newQuantity <= 0) {
      await removeFromCart(itemId);
    } else {
      await updateCartItem(itemId, newQuantity);
    }
  };

  const handleCheckout = () => {
    if (!user) {
      navigate('/login', { state: { from: { pathname: '/checkout' } } });
    } else {
      navigate('/checkout');
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>جاري تحميل السلة...</p>
      </div>
    );
  }

  return (
    <div className="cart-page">
      <div className="container">
        <div className="page-header">
          <h1 className="page-title">سلة التسوق</h1>
          <nav className="breadcrumb">
            <Link to="/">الرئيسية</Link>
            <span>/</span>
            <span>سلة التسوق</span>
          </nav>
        </div>

        {cartItems.length === 0 ? (
          <div className="empty-cart">
            <div className="empty-cart-icon">
              <svg width="80" height="80" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="9" cy="21" r="1"></circle>
                <circle cx="20" cy="21" r="1"></circle>
                <path d="m1 1 4 4 2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
              </svg>
            </div>
            <h2>سلة التسوق فارغة</h2>
            <p>لم تقم بإضافة أي منتجات إلى سلة التسوق بعد</p>
            <Link to="/products" className="btn btn-primary">
              تصفح المنتجات
            </Link>
          </div>
        ) : (
          <div className="cart-content">
            <div className="cart-items">
              <div className="cart-header">
                <h2>المنتجات ({cartItems.length})</h2>
                <button 
                  className="clear-cart-btn"
                  onClick={clearCart}
                >
                  مسح السلة
                </button>
              </div>

              <div className="cart-table">
                <div className="cart-table-header">
                  <div className="col-product">المنتج</div>
                  <div className="col-price">السعر</div>
                  <div className="col-quantity">الكمية</div>
                  <div className="col-total">المجموع</div>
                  <div className="col-actions">إجراءات</div>
                </div>

                <div className="cart-table-body">
                  {cartItems.map(item => (
                    <div key={item.id} className="cart-item">
                      <div className="col-product">
                        <div className="product-info">
                          <Link to={`/products/${item.product.id}`} className="product-image">
                            <img
                              src={item.product.images?.[0] || '/images/product-placeholder.jpg'}
                              alt={item.product.name_ar}
                            />
                          </Link>
                          <div className="product-details">
                            <Link 
                              to={`/products/${item.product.id}`}
                              className="product-name"
                            >
                              {item.product.name_ar}
                            </Link>
                            <p className="product-sku">رقم المنتج: {item.product.sku}</p>
                            {item.product.stock_quantity <= 5 && (
                              <p className="stock-warning">
                                متبقي {item.product.stock_quantity} قطع فقط
                              </p>
                            )}
                          </div>
                        </div>
                      </div>

                      <div className="col-price">
                        {item.product.sale_price ? (
                          <div className="price-container">
                            <span className="current-price">{item.product.sale_price} ريال</span>
                            <span className="original-price">{item.product.price} ريال</span>
                          </div>
                        ) : (
                          <span className="current-price">{item.product.price} ريال</span>
                        )}
                      </div>

                      <div className="col-quantity">
                        <div className="quantity-controls">
                          <button
                            className="quantity-btn"
                            onClick={() => handleQuantityChange(item.id, item.quantity - 1)}
                            disabled={loading}
                          >
                            -
                          </button>
                          <span className="quantity-value">{item.quantity}</span>
                          <button
                            className="quantity-btn"
                            onClick={() => handleQuantityChange(item.id, item.quantity + 1)}
                            disabled={loading || item.quantity >= item.product.stock_quantity}
                          >
                            +
                          </button>
                        </div>
                        <small className="max-quantity">
                          الحد الأقصى: {item.product.stock_quantity}
                        </small>
                      </div>

                      <div className="col-total">
                        <span className="item-total">
                          {((item.product.sale_price || item.product.price) * item.quantity).toFixed(2)} ريال
                        </span>
                      </div>

                      <div className="col-actions">
                        <button
                          className="remove-btn"
                          onClick={() => removeFromCart(item.id)}
                          disabled={loading}
                          title="إزالة من السلة"
                        >
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <polyline points="3,6 5,6 21,6"></polyline>
                            <path d="m19,6v14a2,2 0 0,1-2,2H7a2,2 0 0,1-2-2V6m3,0V4a2,2 0 0,1,2-2h4a2,2 0 0,1,2,2v2"></path>
                            <line x1="10" y1="11" x2="10" y2="17"></line>
                            <line x1="14" y1="11" x2="14" y2="17"></line>
                          </svg>
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="cart-actions">
                <Link to="/products" className="btn btn-outline">
                  متابعة التسوق
                </Link>
              </div>
            </div>

            <div className="cart-summary">
              <div className="summary-card">
                <h3 className="summary-title">ملخص الطلب</h3>
                
                <div className="summary-details">
                  <div className="summary-row">
                    <span>المجموع الفرعي:</span>
                    <span>{cartSubtotal.toFixed(2)} ريال</span>
                  </div>
                  
                  <div className="summary-row">
                    <span>الضريبة (15%):</span>
                    <span>{cartTax.toFixed(2)} ريال</span>
                  </div>
                  
                  <div className="summary-row">
                    <span>الشحن:</span>
                    <span>
                      {cartShipping === 0 ? 'مجاني' : `${cartShipping.toFixed(2)} ريال`}
                    </span>
                  </div>
                  
                  {cartShipping === 0 && cartSubtotal < 200 && (
                    <div className="shipping-note">
                      <small>
                        أضف {(200 - cartSubtotal).toFixed(2)} ريال للحصول على شحن مجاني
                      </small>
                    </div>
                  )}
                  
                  <div className="summary-divider"></div>
                  
                  <div className="summary-row total">
                    <span>المجموع الكلي:</span>
                    <span>{cartTotal.toFixed(2)} ريال</span>
                  </div>
                </div>

                <button 
                  className="btn btn-primary checkout-btn"
                  onClick={handleCheckout}
                  disabled={loading}
                >
                  {loading ? (
                    <>
                      <div className="loading-spinner small"></div>
                      جاري المعالجة...
                    </>
                  ) : (
                    'إتمام الطلب'
                  )}
                </button>

                <div className="payment-methods">
                  <p>طرق الدفع المقبولة:</p>
                  <div className="payment-icons">
                    <div className="payment-icon">💳 Visa</div>
                    <div className="payment-icon">💳 Mastercard</div>
                    <div className="payment-icon">💳 مدى</div>
                    <div className="payment-icon">📱 Apple Pay</div>
                  </div>
                </div>

                <div className="security-badges">
                  <div className="security-item">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path>
                    </svg>
                    <span>دفع آمن</span>
                  </div>
                  <div className="security-item">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                      <circle cx="12" cy="10" r="3"></circle>
                    </svg>
                    <span>شحن سريع</span>
                  </div>
                  <div className="security-item">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                      <polyline points="9,22 9,12 15,12 15,22"></polyline>
                    </svg>
                    <span>إرجاع مجاني</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Cart;
