import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useCart } from '../contexts/CartContext';
import { useAuth } from '../contexts/AuthContext';
import { ordersAPI } from '../services/api';

const Checkout = () => {
  const { 
    cartItems, 
    cartTotal, 
    cartSubtotal, 
    cartTax, 
    cartShipping,
    clearCart 
  } = useCart();
  
  const { user } = useAuth();
  const navigate = useNavigate();

  const [formData, setFormData] = useState({
    // Shipping Address
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    address_line_1: '',
    address_line_2: '',
    city: '',
    state: '',
    postal_code: '',
    country: 'SA',
    
    // Payment
    payment_method: 'credit_card',
    
    // Order Notes
    notes: ''
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [step, setStep] = useState(1); // 1: Shipping, 2: Payment, 3: Review

  useEffect(() => {
    // Redirect if cart is empty
    if (cartItems.length === 0) {
      navigate('/cart');
      return;
    }

    // Redirect if not logged in
    if (!user) {
      navigate('/login', { state: { from: { pathname: '/checkout' } } });
      return;
    }

    // Pre-fill user data
    if (user) {
      setFormData(prev => ({
        ...prev,
        first_name: user.user_metadata?.first_name || '',
        last_name: user.user_metadata?.last_name || '',
        email: user.email || '',
        phone: user.user_metadata?.phone || ''
      }));
    }
  }, [cartItems, user, navigate]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    if (error) setError('');
  };

  const validateStep = (stepNumber) => {
    switch (stepNumber) {
      case 1: // Shipping
        const requiredFields = ['first_name', 'last_name', 'email', 'phone', 'address_line_1', 'city', 'state', 'postal_code'];
        for (const field of requiredFields) {
          if (!formData[field].trim()) {
            setError(`يرجى ملء جميع الحقول المطلوبة`);
            return false;
          }
        }
        break;
      case 2: // Payment
        if (!formData.payment_method) {
          setError('يرجى اختيار طريقة الدفع');
          return false;
        }
        break;
    }
    return true;
  };

  const handleNext = () => {
    if (validateStep(step)) {
      setStep(step + 1);
      setError('');
    }
  };

  const handleBack = () => {
    setStep(step - 1);
    setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!validateStep(step)) {
      return;
    }

    try {
      setLoading(true);
      setError('');

      const orderData = {
        shipping_address: {
          first_name: formData.first_name,
          last_name: formData.last_name,
          email: formData.email,
          phone: formData.phone,
          address_line_1: formData.address_line_1,
          address_line_2: formData.address_line_2,
          city: formData.city,
          state: formData.state,
          postal_code: formData.postal_code,
          country: formData.country
        },
        payment_method: formData.payment_method,
        notes: formData.notes,
        items: cartItems.map(item => ({
          product_id: item.product.id,
          quantity: item.quantity,
          price: item.product.sale_price || item.product.price
        }))
      };

      const response = await ordersAPI.createOrder(orderData);
      
      if (response.data.success) {
        // Clear cart
        await clearCart();
        
        // Redirect to success page
        navigate(`/order-success/${response.data.data.order.id}`);
      } else {
        setError(response.data.message || 'فشل في إنشاء الطلب');
      }
    } catch (error) {
      setError('حدث خطأ أثناء إنشاء الطلب. يرجى المحاولة مرة أخرى.');
    } finally {
      setLoading(false);
    }
  };

  if (cartItems.length === 0) {
    return null; // Will redirect
  }

  return (
    <div className="checkout-page">
      <div className="container">
        <div className="page-header">
          <h1 className="page-title">إتمام الطلب</h1>
          
          {/* Progress Steps */}
          <div className="checkout-steps">
            <div className={`step ${step >= 1 ? 'active' : ''} ${step > 1 ? 'completed' : ''}`}>
              <div className="step-number">1</div>
              <div className="step-label">معلومات الشحن</div>
            </div>
            <div className={`step ${step >= 2 ? 'active' : ''} ${step > 2 ? 'completed' : ''}`}>
              <div className="step-number">2</div>
              <div className="step-label">طريقة الدفع</div>
            </div>
            <div className={`step ${step >= 3 ? 'active' : ''}`}>
              <div className="step-number">3</div>
              <div className="step-label">مراجعة الطلب</div>
            </div>
          </div>
        </div>

        {error && (
          <div className="error-message">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="15" y1="9" x2="9" y2="15"></line>
              <line x1="9" y1="9" x2="15" y2="15"></line>
            </svg>
            {error}
          </div>
        )}

        <div className="checkout-content">
          <div className="checkout-main">
            <form onSubmit={handleSubmit}>
              {/* Step 1: Shipping Information */}
              {step === 1 && (
                <div className="checkout-step">
                  <h2 className="step-title">معلومات الشحن</h2>
                  
                  <div className="form-section">
                    <div className="form-row">
                      <div className="form-group">
                        <label htmlFor="first_name" className="form-label">الاسم الأول *</label>
                        <input
                          type="text"
                          id="first_name"
                          name="first_name"
                          value={formData.first_name}
                          onChange={handleChange}
                          className="form-input"
                          required
                        />
                      </div>
                      <div className="form-group">
                        <label htmlFor="last_name" className="form-label">الاسم الأخير *</label>
                        <input
                          type="text"
                          id="last_name"
                          name="last_name"
                          value={formData.last_name}
                          onChange={handleChange}
                          className="form-input"
                          required
                        />
                      </div>
                    </div>

                    <div className="form-row">
                      <div className="form-group">
                        <label htmlFor="email" className="form-label">البريد الإلكتروني *</label>
                        <input
                          type="email"
                          id="email"
                          name="email"
                          value={formData.email}
                          onChange={handleChange}
                          className="form-input"
                          required
                        />
                      </div>
                      <div className="form-group">
                        <label htmlFor="phone" className="form-label">رقم الهاتف *</label>
                        <input
                          type="tel"
                          id="phone"
                          name="phone"
                          value={formData.phone}
                          onChange={handleChange}
                          className="form-input"
                          placeholder="05xxxxxxxx"
                          required
                        />
                      </div>
                    </div>

                    <div className="form-group">
                      <label htmlFor="address_line_1" className="form-label">العنوان الأول *</label>
                      <input
                        type="text"
                        id="address_line_1"
                        name="address_line_1"
                        value={formData.address_line_1}
                        onChange={handleChange}
                        className="form-input"
                        placeholder="رقم المبنى، اسم الشارع"
                        required
                      />
                    </div>

                    <div className="form-group">
                      <label htmlFor="address_line_2" className="form-label">العنوان الثاني (اختياري)</label>
                      <input
                        type="text"
                        id="address_line_2"
                        name="address_line_2"
                        value={formData.address_line_2}
                        onChange={handleChange}
                        className="form-input"
                        placeholder="رقم الشقة، الطابق، إلخ"
                      />
                    </div>

                    <div className="form-row">
                      <div className="form-group">
                        <label htmlFor="city" className="form-label">المدينة *</label>
                        <input
                          type="text"
                          id="city"
                          name="city"
                          value={formData.city}
                          onChange={handleChange}
                          className="form-input"
                          required
                        />
                      </div>
                      <div className="form-group">
                        <label htmlFor="state" className="form-label">المنطقة *</label>
                        <select
                          id="state"
                          name="state"
                          value={formData.state}
                          onChange={handleChange}
                          className="form-select"
                          required
                        >
                          <option value="">اختر المنطقة</option>
                          <option value="riyadh">الرياض</option>
                          <option value="makkah">مكة المكرمة</option>
                          <option value="madinah">المدينة المنورة</option>
                          <option value="eastern">المنطقة الشرقية</option>
                          <option value="asir">عسير</option>
                          <option value="tabuk">تبوك</option>
                          <option value="qassim">القصيم</option>
                          <option value="hail">حائل</option>
                          <option value="northern">الحدود الشمالية</option>
                          <option value="jazan">جازان</option>
                          <option value="najran">نجران</option>
                          <option value="bahah">الباحة</option>
                          <option value="jouf">الجوف</option>
                        </select>
                      </div>
                      <div className="form-group">
                        <label htmlFor="postal_code" className="form-label">الرمز البريدي *</label>
                        <input
                          type="text"
                          id="postal_code"
                          name="postal_code"
                          value={formData.postal_code}
                          onChange={handleChange}
                          className="form-input"
                          placeholder="12345"
                          required
                        />
                      </div>
                    </div>
                  </div>

                  <div className="step-actions">
                    <button type="button" className="btn btn-primary" onClick={handleNext}>
                      التالي
                    </button>
                  </div>
                </div>
              )}

              {/* Step 2: Payment Method */}
              {step === 2 && (
                <div className="checkout-step">
                  <h2 className="step-title">طريقة الدفع</h2>
                  
                  <div className="payment-methods">
                    <div className="payment-option">
                      <label className="payment-label">
                        <input
                          type="radio"
                          name="payment_method"
                          value="credit_card"
                          checked={formData.payment_method === 'credit_card'}
                          onChange={handleChange}
                        />
                        <div className="payment-info">
                          <div className="payment-title">بطاقة ائتمان</div>
                          <div className="payment-description">Visa, Mastercard, مدى</div>
                        </div>
                        <div className="payment-icons">
                          <span>💳</span>
                        </div>
                      </label>
                    </div>

                    <div className="payment-option">
                      <label className="payment-label">
                        <input
                          type="radio"
                          name="payment_method"
                          value="apple_pay"
                          checked={formData.payment_method === 'apple_pay'}
                          onChange={handleChange}
                        />
                        <div className="payment-info">
                          <div className="payment-title">Apple Pay</div>
                          <div className="payment-description">دفع سريع وآمن</div>
                        </div>
                        <div className="payment-icons">
                          <span>📱</span>
                        </div>
                      </label>
                    </div>

                    <div className="payment-option">
                      <label className="payment-label">
                        <input
                          type="radio"
                          name="payment_method"
                          value="cash_on_delivery"
                          checked={formData.payment_method === 'cash_on_delivery'}
                          onChange={handleChange}
                        />
                        <div className="payment-info">
                          <div className="payment-title">الدفع عند الاستلام</div>
                          <div className="payment-description">ادفع نقداً عند وصول الطلب</div>
                        </div>
                        <div className="payment-icons">
                          <span>💵</span>
                        </div>
                      </label>
                    </div>
                  </div>

                  <div className="form-group">
                    <label htmlFor="notes" className="form-label">ملاحظات الطلب (اختياري)</label>
                    <textarea
                      id="notes"
                      name="notes"
                      value={formData.notes}
                      onChange={handleChange}
                      className="form-textarea"
                      rows="4"
                      placeholder="أي ملاحظات خاصة بطلبك..."
                    ></textarea>
                  </div>

                  <div className="step-actions">
                    <button type="button" className="btn btn-outline" onClick={handleBack}>
                      السابق
                    </button>
                    <button type="button" className="btn btn-primary" onClick={handleNext}>
                      مراجعة الطلب
                    </button>
                  </div>
                </div>
              )}

              {/* Step 3: Order Review */}
              {step === 3 && (
                <div className="checkout-step">
                  <h2 className="step-title">مراجعة الطلب</h2>
                  
                  <div className="order-review">
                    <div className="review-section">
                      <h3>معلومات الشحن</h3>
                      <div className="review-content">
                        <p><strong>{formData.first_name} {formData.last_name}</strong></p>
                        <p>{formData.email}</p>
                        <p>{formData.phone}</p>
                        <p>{formData.address_line_1}</p>
                        {formData.address_line_2 && <p>{formData.address_line_2}</p>}
                        <p>{formData.city}, {formData.state} {formData.postal_code}</p>
                      </div>
                    </div>

                    <div className="review-section">
                      <h3>طريقة الدفع</h3>
                      <div className="review-content">
                        <p>
                          {formData.payment_method === 'credit_card' && 'بطاقة ائتمان'}
                          {formData.payment_method === 'apple_pay' && 'Apple Pay'}
                          {formData.payment_method === 'cash_on_delivery' && 'الدفع عند الاستلام'}
                        </p>
                      </div>
                    </div>

                    {formData.notes && (
                      <div className="review-section">
                        <h3>ملاحظات الطلب</h3>
                        <div className="review-content">
                          <p>{formData.notes}</p>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="step-actions">
                    <button type="button" className="btn btn-outline" onClick={handleBack}>
                      السابق
                    </button>
                    <button 
                      type="submit" 
                      className="btn btn-primary"
                      disabled={loading}
                    >
                      {loading ? (
                        <>
                          <div className="loading-spinner small"></div>
                          جاري إنشاء الطلب...
                        </>
                      ) : (
                        'تأكيد الطلب'
                      )}
                    </button>
                  </div>
                </div>
              )}
            </form>
          </div>

          {/* Order Summary Sidebar */}
          <div className="checkout-sidebar">
            <div className="order-summary">
              <h3 className="summary-title">ملخص الطلب</h3>
              
              <div className="order-items">
                {cartItems.map(item => (
                  <div key={item.id} className="order-item">
                    <div className="item-image">
                      <img
                        src={item.product.images?.[0] || '/images/product-placeholder.jpg'}
                        alt={item.product.name_ar}
                      />
                      <span className="item-quantity">{item.quantity}</span>
                    </div>
                    <div className="item-details">
                      <div className="item-name">{item.product.name_ar}</div>
                      <div className="item-price">
                        {((item.product.sale_price || item.product.price) * item.quantity).toFixed(2)} ريال
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="summary-totals">
                <div className="summary-row">
                  <span>المجموع الفرعي:</span>
                  <span>{cartSubtotal.toFixed(2)} ريال</span>
                </div>
                <div className="summary-row">
                  <span>الضريبة (15%):</span>
                  <span>{cartTax.toFixed(2)} ريال</span>
                </div>
                <div className="summary-row">
                  <span>الشحن:</span>
                  <span>
                    {cartShipping === 0 ? 'مجاني' : `${cartShipping.toFixed(2)} ريال`}
                  </span>
                </div>
                <div className="summary-divider"></div>
                <div className="summary-row total">
                  <span>المجموع الكلي:</span>
                  <span>{cartTotal.toFixed(2)} ريال</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Checkout;
