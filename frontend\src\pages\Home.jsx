import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { productsAPI, categoriesAPI } from '../services/api';

const Home = () => {
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [popularCategories, setPopularCategories] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadHomeData();
  }, []);

  const loadHomeData = async () => {
    console.log('Loading home data...');
    try {
      setLoading(true);

      // Load featured products and popular categories in parallel
      const [productsResponse, categoriesResponse] = await Promise.all([
        productsAPI.getFeaturedProducts(8),
        categoriesAPI.getPopularCategories(6)
      ]);

      console.log('Products Response:', productsResponse.data);
      console.log('Categories Response:', categoriesResponse.data);

      if (productsResponse.data.success) {
        setFeaturedProducts(productsResponse.data.products);
      }

      if (categoriesResponse.data.success) {
        setPopularCategories(categoriesResponse.data.categories);
      }
    } catch (error) {
      console.error('Error loading home data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>جاري التحميل...</p>
      </div>
    );
  }

  return (
    <div className="home-page">
      {/* Hero Section */}
      <section className="hero">
        <div className="container">
          <div className="hero-content">
            <div className="hero-text">
              <h1 className="hero-title">مرحباً بك في متجر شغف</h1>
              <p className="hero-description">
                اكتشف مجموعة واسعة من المنتجات عالية الجودة بأفضل الأسعار.
                تسوق الآن واستمتع بتجربة تسوق مميزة مع خدمة توصيل سريعة.
              </p>
              <div className="hero-actions">
                <Link to="/products" className="btn btn-primary">تسوق الآن</Link>
                <Link to="/categories" className="btn btn-outline">تصفح الفئات</Link>
              </div>
            </div>
            <div className="hero-image">
              <img src="/images/hero-image.jpg" alt="متجر شغف" />
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features">
        <div className="container">
          <div className="features-grid">
            <div className="feature-card">
              <div className="feature-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                  <polyline points="3.27,6.96 12,12.01 20.73,6.96"></polyline>
                  <line x1="12" y1="22.08" x2="12" y2="12"></line>
                </svg>
              </div>
              <h3 className="feature-title">شحن مجاني</h3>
              <p className="feature-description">شحن مجاني لجميع الطلبات أكثر من 200 ريال</p>
            </div>

            <div className="feature-card">
              <div className="feature-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polyline points="12,6 12,12 16,14"></polyline>
                </svg>
              </div>
              <h3 className="feature-title">توصيل سريع</h3>
              <p className="feature-description">توصيل خلال 24-48 ساعة داخل المدن الرئيسية</p>
            </div>

            <div className="feature-card">
              <div className="feature-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M9 12l2 2 4-4"></path>
                  <path d="M21 12c.552 0 1-.448 1-1V5c0-.552-.448-1-1-1H3c-.552 0-1 .448-1 1v6c0 .552.448 1 1 1h9l4-4-4-4H3"></path>
                </svg>
              </div>
              <h3 className="feature-title">ضمان الجودة</h3>
              <p className="feature-description">ضمان على جميع المنتجات مع إمكانية الإرجاع</p>
            </div>

            <div className="feature-card">
              <div className="feature-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                </svg>
              </div>
              <h3 className="feature-title">دعم 24/7</h3>
              <p className="feature-description">خدمة عملاء متاحة على مدار الساعة</p>
            </div>
          </div>
        </div>
      </section>

      {/* Popular Categories */}
      {popularCategories.length > 0 && (
        <section className="popular-categories">
          <div className="container">
            <div className="section-header">
              <h2 className="section-title">الفئات الشائعة</h2>
              <Link to="/categories" className="section-link">عرض جميع الفئات</Link>
            </div>
            <div className="categories-grid">
              {popularCategories.map((category) => (
                <Link
                  key={category.id}
                  to={`/categories/${category.id}`}
                  className="category-card"
                >
                  <div className="category-image">
                    <img
                      src={category.image_url || '/images/category-placeholder.jpg'}
                      alt={category.name_ar}
                    />
                  </div>
                  <div className="category-info">
                    <h3 className="category-name">{category.name_ar}</h3>
                    <p className="category-count">{category.product_count} منتج</p>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Featured Products */}
      {featuredProducts.length > 0 && (
        <section className="featured-products">
          <div className="container">
            <div className="section-header">
              <h2 className="section-title">المنتجات المميزة</h2>
              <Link to="/products" className="section-link">عرض جميع المنتجات</Link>
            </div>
            <div className="products-grid">
              {featuredProducts.map((product) => (
                <div key={product.id} className="product-card">
                  <Link to={`/products/${product.id}`} className="product-link">
                    <div className="product-image">
                      <img
                        src={product.images?.[0] || '/images/product-placeholder.jpg'}
                        alt={product.name_ar}
                      />
                      {product.sale_price && (
                        <div className="product-badge">خصم</div>
                      )}
                    </div>
                    <div className="product-info">
                      <h3 className="product-name">{product.name_ar}</h3>
                      <div className="product-price">
                        {product.sale_price ? (
                          <>
                            <span className="current-price">{product.sale_price} ريال</span>
                            <span className="original-price">{product.price} ريال</span>
                          </>
                        ) : (
                          <span className="current-price">{product.price} ريال</span>
                        )}
                      </div>
                    </div>
                  </Link>
                  <button className="add-to-cart-btn">
                    إضافة للسلة
                  </button>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Newsletter Section */}
      <section className="newsletter">
        <div className="container">
          <div className="newsletter-content">
            <div className="newsletter-text">
              <h2 className="newsletter-title">اشترك في النشرة الإخبارية</h2>
              <p className="newsletter-description">
                احصل على آخر العروض والمنتجات الجديدة مباشرة في بريدك الإلكتروني
              </p>
            </div>
            <form className="newsletter-form">
              <input
                type="email"
                placeholder="أدخل بريدك الإلكتروني"
                className="newsletter-input"
                required
              />
              <button type="submit" className="newsletter-btn">
                اشتراك
              </button>
            </form>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
