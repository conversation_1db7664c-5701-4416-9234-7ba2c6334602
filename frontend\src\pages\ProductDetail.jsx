import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { productsAPI } from '../services/api';
import { useCart } from '../contexts/CartContext';

const ProductDetail = () => {
  const { id } = useParams();
  const [product, setProduct] = useState(null);
  const [relatedProducts, setRelatedProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedImage, setSelectedImage] = useState(0);
  const [quantity, setQuantity] = useState(1);
  const [activeTab, setActiveTab] = useState('description');
  
  const { addToCart, isInCart, getCartItem } = useCart();

  useEffect(() => {
    if (id) {
      loadProduct();
    }
  }, [id]);

  const loadProduct = async () => {
    try {
      setLoading(true);
      const response = await productsAPI.getProduct(id);
      
      if (response.data.success) {
        setProduct(response.data.data.product);
        setRelatedProducts(response.data.data.related_products || []);
      }
    } catch (error) {
      console.error('Error loading product:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAddToCart = async () => {
    try {
      const result = await addToCart(product.id, quantity);
      if (result.success) {
        // Show success message
        console.log('Product added to cart successfully');
      } else {
        console.error('Failed to add product to cart:', result.message);
      }
    } catch (error) {
      console.error('Error adding product to cart:', error);
    }
  };

  const handleQuantityChange = (newQuantity) => {
    if (newQuantity >= 1 && newQuantity <= product.stock_quantity) {
      setQuantity(newQuantity);
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>جاري تحميل المنتج...</p>
      </div>
    );
  }

  if (!product) {
    return (
      <div className="error-container">
        <h2>المنتج غير موجود</h2>
        <p>عذراً، لم يتم العثور على المنتج المطلوب</p>
        <Link to="/products" className="btn btn-primary">العودة للمنتجات</Link>
      </div>
    );
  }

  const cartItem = getCartItem(product.id);
  const isProductInCart = isInCart(product.id);

  return (
    <div className="product-detail-page">
      <div className="container">
        {/* Breadcrumb */}
        <nav className="breadcrumb">
          <Link to="/">الرئيسية</Link>
          <span>/</span>
          <Link to="/products">المنتجات</Link>
          <span>/</span>
          <span>{product.name_ar}</span>
        </nav>

        {/* Product Details */}
        <div className="product-detail">
          {/* Product Images */}
          <div className="product-images">
            <div className="main-image">
              <img
                src={product.images?.[selectedImage] || '/images/product-placeholder.jpg'}
                alt={product.name_ar}
              />
              {product.sale_price && (
                <div className="product-badge">خصم</div>
              )}
            </div>
            {product.images && product.images.length > 1 && (
              <div className="image-thumbnails">
                {product.images.map((image, index) => (
                  <button
                    key={index}
                    className={`thumbnail ${index === selectedImage ? 'active' : ''}`}
                    onClick={() => setSelectedImage(index)}
                  >
                    <img src={image} alt={`${product.name_ar} ${index + 1}`} />
                  </button>
                ))}
              </div>
            )}
          </div>

          {/* Product Info */}
          <div className="product-info">
            <h1 className="product-title">{product.name_ar}</h1>
            {product.name_en && (
              <h2 className="product-title-en">{product.name_en}</h2>
            )}

            <div className="product-rating">
              <div className="stars">
                {[1, 2, 3, 4, 5].map(star => (
                  <svg
                    key={star}
                    className={`star ${star <= (product.average_rating || 0) ? 'filled' : ''}`}
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                  >
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" />
                  </svg>
                ))}
              </div>
              <span className="rating-text">
                ({product.reviews_count || 0} تقييم)
              </span>
            </div>

            <div className="product-price">
              {product.sale_price ? (
                <>
                  <span className="current-price">{product.sale_price} ريال</span>
                  <span className="original-price">{product.price} ريال</span>
                  <span className="discount-percentage">
                    خصم {Math.round(((product.price - product.sale_price) / product.price) * 100)}%
                  </span>
                </>
              ) : (
                <span className="current-price">{product.price} ريال</span>
              )}
            </div>

            <div className="product-description">
              <p>{product.short_description_ar}</p>
            </div>

            <div className="product-meta">
              <div className="meta-item">
                <span className="meta-label">الفئة:</span>
                <Link to={`/categories/${product.categories?.id}`} className="meta-value">
                  {product.categories?.name_ar}
                </Link>
              </div>
              <div className="meta-item">
                <span className="meta-label">رقم المنتج:</span>
                <span className="meta-value">{product.sku}</span>
              </div>
              <div className="meta-item">
                <span className="meta-label">الحالة:</span>
                <span className={`meta-value ${product.stock_quantity > 0 ? 'in-stock' : 'out-of-stock'}`}>
                  {product.stock_quantity > 0 ? `متوفر (${product.stock_quantity} قطعة)` : 'نفد المخزون'}
                </span>
              </div>
            </div>

            {/* Add to Cart Section */}
            {product.stock_quantity > 0 && (
              <div className="add-to-cart-section">
                <div className="quantity-selector">
                  <label>الكمية:</label>
                  <div className="quantity-controls">
                    <button
                      className="quantity-btn"
                      onClick={() => handleQuantityChange(quantity - 1)}
                      disabled={quantity <= 1}
                    >
                      -
                    </button>
                    <span className="quantity-value">{quantity}</span>
                    <button
                      className="quantity-btn"
                      onClick={() => handleQuantityChange(quantity + 1)}
                      disabled={quantity >= product.stock_quantity}
                    >
                      +
                    </button>
                  </div>
                </div>

                <div className="cart-actions">
                  <button
                    className="btn btn-primary add-to-cart"
                    onClick={handleAddToCart}
                  >
                    {isProductInCart ? 'تحديث السلة' : 'إضافة للسلة'}
                  </button>
                  <button className="btn btn-outline wishlist">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
                    </svg>
                    إضافة للمفضلة
                  </button>
                </div>
              </div>
            )}

            {/* Share */}
            <div className="product-share">
              <span>مشاركة:</span>
              <div className="share-buttons">
                <button className="share-btn">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                </button>
                <button className="share-btn">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                  </svg>
                </button>
                <button className="share-btn">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Product Tabs */}
        <div className="product-tabs">
          <div className="tab-headers">
            <button
              className={`tab-header ${activeTab === 'description' ? 'active' : ''}`}
              onClick={() => setActiveTab('description')}
            >
              الوصف
            </button>
            <button
              className={`tab-header ${activeTab === 'specifications' ? 'active' : ''}`}
              onClick={() => setActiveTab('specifications')}
            >
              المواصفات
            </button>
            <button
              className={`tab-header ${activeTab === 'reviews' ? 'active' : ''}`}
              onClick={() => setActiveTab('reviews')}
            >
              التقييمات ({product.reviews_count || 0})
            </button>
          </div>

          <div className="tab-content">
            {activeTab === 'description' && (
              <div className="tab-pane">
                <div className="description-content">
                  <p>{product.description_ar}</p>
                  {product.description_en && (
                    <p className="description-en">{product.description_en}</p>
                  )}
                </div>
              </div>
            )}

            {activeTab === 'specifications' && (
              <div className="tab-pane">
                <div className="specifications">
                  {product.weight && (
                    <div className="spec-item">
                      <span className="spec-label">الوزن:</span>
                      <span className="spec-value">{product.weight} كجم</span>
                    </div>
                  )}
                  {product.dimensions && (
                    <div className="spec-item">
                      <span className="spec-label">الأبعاد:</span>
                      <span className="spec-value">{product.dimensions}</span>
                    </div>
                  )}
                  <div className="spec-item">
                    <span className="spec-label">رقم المنتج:</span>
                    <span className="spec-value">{product.sku}</span>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'reviews' && (
              <div className="tab-pane">
                <div className="reviews-section">
                  <p>لا توجد تقييمات بعد. كن أول من يقيم هذا المنتج!</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Related Products */}
        {relatedProducts.length > 0 && (
          <section className="related-products">
            <h2 className="section-title">منتجات ذات صلة</h2>
            <div className="products-grid">
              {relatedProducts.map(relatedProduct => (
                <div key={relatedProduct.id} className="product-card">
                  <Link to={`/products/${relatedProduct.id}`} className="product-link">
                    <div className="product-image">
                      <img
                        src={relatedProduct.images?.[0] || '/images/product-placeholder.jpg'}
                        alt={relatedProduct.name_ar}
                      />
                    </div>
                    <div className="product-info">
                      <h3 className="product-name">{relatedProduct.name_ar}</h3>
                      <div className="product-price">
                        {relatedProduct.sale_price ? (
                          <>
                            <span className="current-price">{relatedProduct.sale_price} ريال</span>
                            <span className="original-price">{relatedProduct.price} ريال</span>
                          </>
                        ) : (
                          <span className="current-price">{relatedProduct.price} ريال</span>
                        )}
                      </div>
                    </div>
                  </Link>
                </div>
              ))}
            </div>
          </section>
        )}
      </div>
    </div>
  );
};

export default ProductDetail;
