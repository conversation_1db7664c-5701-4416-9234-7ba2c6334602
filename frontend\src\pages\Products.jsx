import React, { useState, useEffect } from 'react';
import { Link, useSearchParams } from 'react-router-dom';
import { productsAPI, categoriesAPI } from '../services/api';
import { useCart } from '../contexts/CartContext';

const Products = () => {
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({});
  const [searchParams, setSearchParams] = useSearchParams();
  
  const { addToCart } = useCart();

  // Filter states
  const [filters, setFilters] = useState({
    search: searchParams.get('search') || '',
    category_id: searchParams.get('category_id') || '',
    min_price: searchParams.get('min_price') || '',
    max_price: searchParams.get('max_price') || '',
    sort_by: searchParams.get('sort_by') || 'created_at',
    sort_order: searchParams.get('sort_order') || 'desc',
    page: parseInt(searchParams.get('page')) || 1
  });

  useEffect(() => {
    loadCategories();
  }, []);

  useEffect(() => {
    loadProducts();
  }, [filters]);

  const loadCategories = async () => {
    try {
      const response = await categoriesAPI.getCategories();
      if (response.data.success) {
        setCategories(response.data.data.categories);
      }
    } catch (error) {
      console.error('Error loading categories:', error);
    }
  };

  const loadProducts = async () => {
    try {
      setLoading(true);
      
      // Update URL params
      const params = new URLSearchParams();
      Object.entries(filters).forEach(([key, value]) => {
        if (value) params.set(key, value);
      });
      setSearchParams(params);

      const response = await productsAPI.getProducts(filters);
      
      if (response.data.success) {
        setProducts(response.data.data.products);
        setPagination(response.data.data.pagination);
      }
    } catch (error) {
      console.error('Error loading products:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset to first page when filters change
    }));
  };

  const handlePageChange = (page) => {
    setFilters(prev => ({ ...prev, page }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const handleAddToCart = async (productId) => {
    try {
      const result = await addToCart(productId, 1);
      if (result.success) {
        // Show success message
        console.log('Product added to cart successfully');
      } else {
        console.error('Failed to add product to cart:', result.message);
      }
    } catch (error) {
      console.error('Error adding product to cart:', error);
    }
  };

  return (
    <div className="products-page">
      <div className="container">
        {/* Page Header */}
        <div className="page-header">
          <h1 className="page-title">المنتجات</h1>
          <p className="page-subtitle">اكتشف مجموعة واسعة من المنتجات عالية الجودة</p>
        </div>

        <div className="products-layout">
          {/* Filters Sidebar */}
          <aside className="filters-sidebar">
            <div className="filters-header">
              <h3>تصفية النتائج</h3>
              <button 
                className="clear-filters-btn"
                onClick={() => setFilters({
                  search: '',
                  category_id: '',
                  min_price: '',
                  max_price: '',
                  sort_by: 'created_at',
                  sort_order: 'desc',
                  page: 1
                })}
              >
                مسح الفلاتر
              </button>
            </div>

            {/* Search Filter */}
            <div className="filter-group">
              <label className="filter-label">البحث</label>
              <input
                type="text"
                className="filter-input"
                placeholder="ابحث عن المنتجات..."
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
              />
            </div>

            {/* Category Filter */}
            <div className="filter-group">
              <label className="filter-label">الفئة</label>
              <select
                className="filter-select"
                value={filters.category_id}
                onChange={(e) => handleFilterChange('category_id', e.target.value)}
              >
                <option value="">جميع الفئات</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name_ar}
                  </option>
                ))}
              </select>
            </div>

            {/* Price Range Filter */}
            <div className="filter-group">
              <label className="filter-label">نطاق السعر</label>
              <div className="price-range">
                <input
                  type="number"
                  className="filter-input"
                  placeholder="من"
                  value={filters.min_price}
                  onChange={(e) => handleFilterChange('min_price', e.target.value)}
                />
                <span>إلى</span>
                <input
                  type="number"
                  className="filter-input"
                  placeholder="إلى"
                  value={filters.max_price}
                  onChange={(e) => handleFilterChange('max_price', e.target.value)}
                />
              </div>
            </div>

            {/* Sort Filter */}
            <div className="filter-group">
              <label className="filter-label">ترتيب حسب</label>
              <select
                className="filter-select"
                value={`${filters.sort_by}-${filters.sort_order}`}
                onChange={(e) => {
                  const [sort_by, sort_order] = e.target.value.split('-');
                  handleFilterChange('sort_by', sort_by);
                  handleFilterChange('sort_order', sort_order);
                }}
              >
                <option value="created_at-desc">الأحدث</option>
                <option value="created_at-asc">الأقدم</option>
                <option value="price-asc">السعر: من الأقل للأعلى</option>
                <option value="price-desc">السعر: من الأعلى للأقل</option>
                <option value="name_ar-asc">الاسم: أ-ي</option>
                <option value="name_ar-desc">الاسم: ي-أ</option>
              </select>
            </div>
          </aside>

          {/* Products Grid */}
          <main className="products-main">
            {/* Results Header */}
            <div className="results-header">
              <div className="results-info">
                {pagination.total && (
                  <span>عرض {pagination.total} منتج</span>
                )}
              </div>
              <div className="view-options">
                <button className="view-btn active">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <rect x="3" y="3" width="7" height="7"></rect>
                    <rect x="14" y="3" width="7" height="7"></rect>
                    <rect x="14" y="14" width="7" height="7"></rect>
                    <rect x="3" y="14" width="7" height="7"></rect>
                  </svg>
                </button>
                <button className="view-btn">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <line x1="8" y1="6" x2="21" y2="6"></line>
                    <line x1="8" y1="12" x2="21" y2="12"></line>
                    <line x1="8" y1="18" x2="21" y2="18"></line>
                    <line x1="3" y1="6" x2="3.01" y2="6"></line>
                    <line x1="3" y1="12" x2="3.01" y2="12"></line>
                    <line x1="3" y1="18" x2="3.01" y2="18"></line>
                  </svg>
                </button>
              </div>
            </div>

            {/* Loading State */}
            {loading ? (
              <div className="loading-container">
                <div className="loading-spinner"></div>
                <p>جاري تحميل المنتجات...</p>
              </div>
            ) : (
              <>
                {/* Products Grid */}
                {products.length > 0 ? (
                  <div className="products-grid">
                    {products.map(product => (
                      <div key={product.id} className="product-card">
                        <Link to={`/products/${product.id}`} className="product-link">
                          <div className="product-image">
                            <img
                              src={product.images?.[0] || '/images/product-placeholder.jpg'}
                              alt={product.name_ar}
                              loading="lazy"
                            />
                            {product.sale_price && (
                              <div className="product-badge">خصم</div>
                            )}
                            {product.stock_quantity === 0 && (
                              <div className="product-badge out-of-stock">نفد المخزون</div>
                            )}
                          </div>
                          <div className="product-info">
                            <h3 className="product-name">{product.name_ar}</h3>
                            <p className="product-description">{product.short_description_ar}</p>
                            <div className="product-price">
                              {product.sale_price ? (
                                <>
                                  <span className="current-price">{product.sale_price} ريال</span>
                                  <span className="original-price">{product.price} ريال</span>
                                </>
                              ) : (
                                <span className="current-price">{product.price} ريال</span>
                              )}
                            </div>
                          </div>
                        </Link>
                        <button
                          className="add-to-cart-btn"
                          onClick={() => handleAddToCart(product.id)}
                          disabled={product.stock_quantity === 0}
                        >
                          {product.stock_quantity === 0 ? 'نفد المخزون' : 'إضافة للسلة'}
                        </button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="no-products">
                    <svg width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                      <circle cx="11" cy="11" r="8"></circle>
                      <path d="m21 21-4.35-4.35"></path>
                    </svg>
                    <h3>لا توجد منتجات</h3>
                    <p>لم يتم العثور على منتجات تطابق معايير البحث</p>
                  </div>
                )}

                {/* Pagination */}
                {pagination.total_pages > 1 && (
                  <div className="pagination">
                    <button
                      className="pagination-btn"
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={!pagination.has_prev}
                    >
                      السابق
                    </button>
                    
                    <div className="pagination-numbers">
                      {Array.from({ length: pagination.total_pages }, (_, i) => i + 1).map(page => (
                        <button
                          key={page}
                          className={`pagination-number ${page === pagination.page ? 'active' : ''}`}
                          onClick={() => handlePageChange(page)}
                        >
                          {page}
                        </button>
                      ))}
                    </div>
                    
                    <button
                      className="pagination-btn"
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={!pagination.has_next}
                    >
                      التالي
                    </button>
                  </div>
                )}
              </>
            )}
          </main>
        </div>
      </div>
    </div>
  );
};

export default Products;
