import React, { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { authAPI } from '../services/api';

const Profile = () => {
  const [profileData, setProfileData] = useState({
    first_name: '',
    last_name: '',
    email: '',
    phone: '',
    date_of_birth: '',
    gender: ''
  });
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  
  const { user, updateProfile } = useAuth();

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    try {
      setLoading(true);
      const response = await authAPI.getProfile();
      
      if (response.data.success) {
        setProfileData(response.data.data.user);
      }
    } catch (error) {
      console.error('Error loading profile:', error);
      setError('خطأ في تحميل بيانات الملف الشخصي');
    } finally {
      setLoading(false);
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setProfileData(prev => ({
      ...prev,
      [name]: value
    }));
    // Clear messages when user starts typing
    if (error) setError('');
    if (success) setSuccess('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      setUpdating(true);
      setError('');
      setSuccess('');
      
      const response = await authAPI.updateProfile(profileData);
      
      if (response.data.success) {
        setSuccess('تم تحديث الملف الشخصي بنجاح');
        // Update auth context
        await updateProfile(profileData);
      } else {
        setError(response.data.message || 'فشل في تحديث الملف الشخصي');
      }
    } catch (error) {
      setError('حدث خطأ أثناء تحديث الملف الشخصي');
    } finally {
      setUpdating(false);
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>جاري تحميل الملف الشخصي...</p>
      </div>
    );
  }

  return (
    <div className="profile-page">
      <div className="container">
        <div className="profile-container">
          <div className="profile-header">
            <h1 className="profile-title">الملف الشخصي</h1>
            <p className="profile-subtitle">إدارة معلوماتك الشخصية</p>
          </div>

          {error && (
            <div className="error-message">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="15" y1="9" x2="9" y2="15"></line>
                <line x1="9" y1="9" x2="15" y2="15"></line>
              </svg>
              {error}
            </div>
          )}

          {success && (
            <div className="success-message">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                <polyline points="22,4 12,14.01 9,11.01"></polyline>
              </svg>
              {success}
            </div>
          )}

          <div className="profile-content">
            <div className="profile-sidebar">
              <div className="profile-avatar">
                <div className="avatar-circle">
                  {profileData.first_name?.charAt(0) || user?.email?.charAt(0) || 'U'}
                </div>
                <button className="avatar-upload-btn">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
                    <circle cx="12" cy="13" r="4"></circle>
                  </svg>
                  تغيير الصورة
                </button>
              </div>

              <div className="profile-menu">
                <button className="menu-item active">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                  </svg>
                  المعلومات الشخصية
                </button>
                <button className="menu-item">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                    <circle cx="12" cy="10" r="3"></circle>
                  </svg>
                  العناوين
                </button>
                <button className="menu-item">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                    <rect x="1" y="3" width="15" height="13"></rect>
                    <path d="m16 8 2 2-2 2"></path>
                    <path d="m22 12-6 0"></path>
                  </svg>
                  كلمة المرور
                </button>
              </div>
            </div>

            <div className="profile-main">
              <form onSubmit={handleSubmit} className="profile-form">
                <div className="form-section">
                  <h3 className="section-title">المعلومات الأساسية</h3>
                  
                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="first_name" className="form-label">الاسم الأول</label>
                      <input
                        type="text"
                        id="first_name"
                        name="first_name"
                        value={profileData.first_name}
                        onChange={handleChange}
                        className="form-input"
                        placeholder="أدخل اسمك الأول"
                        required
                        disabled={updating}
                      />
                    </div>

                    <div className="form-group">
                      <label htmlFor="last_name" className="form-label">الاسم الأخير</label>
                      <input
                        type="text"
                        id="last_name"
                        name="last_name"
                        value={profileData.last_name}
                        onChange={handleChange}
                        className="form-input"
                        placeholder="أدخل اسمك الأخير"
                        required
                        disabled={updating}
                      />
                    </div>
                  </div>

                  <div className="form-group">
                    <label htmlFor="email" className="form-label">البريد الإلكتروني</label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={profileData.email}
                      className="form-input"
                      disabled
                      title="لا يمكن تغيير البريد الإلكتروني"
                    />
                    <small className="form-help">لا يمكن تغيير البريد الإلكتروني</small>
                  </div>

                  <div className="form-group">
                    <label htmlFor="phone" className="form-label">رقم الهاتف</label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={profileData.phone}
                      onChange={handleChange}
                      className="form-input"
                      placeholder="05xxxxxxxx"
                      disabled={updating}
                    />
                  </div>

                  <div className="form-row">
                    <div className="form-group">
                      <label htmlFor="date_of_birth" className="form-label">تاريخ الميلاد</label>
                      <input
                        type="date"
                        id="date_of_birth"
                        name="date_of_birth"
                        value={profileData.date_of_birth}
                        onChange={handleChange}
                        className="form-input"
                        disabled={updating}
                      />
                    </div>

                    <div className="form-group">
                      <label htmlFor="gender" className="form-label">الجنس</label>
                      <select
                        id="gender"
                        name="gender"
                        value={profileData.gender}
                        onChange={handleChange}
                        className="form-select"
                        disabled={updating}
                      >
                        <option value="">اختر الجنس</option>
                        <option value="male">ذكر</option>
                        <option value="female">أنثى</option>
                      </select>
                    </div>
                  </div>
                </div>

                <div className="form-actions">
                  <button
                    type="submit"
                    className="btn btn-primary"
                    disabled={updating}
                  >
                    {updating ? (
                      <>
                        <div className="loading-spinner small"></div>
                        جاري الحفظ...
                      </>
                    ) : (
                      'حفظ التغييرات'
                    )}
                  </button>
                  <button
                    type="button"
                    className="btn btn-outline"
                    onClick={loadProfile}
                    disabled={updating}
                  >
                    إلغاء
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;
