/* Main CSS File - Arabic E-commerce Website */

/* CSS Variables */
:root {
  /* Colors */
  --primary-color: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #3b82f6;
  --secondary-color: #64748b;
  --accent-color: #f59e0b;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --error-color: #ef4444;
  
  /* Neutral Colors */
  --white: #ffffff;
  --gray-50: #f8fafc;
  --gray-100: #f1f5f9;
  --gray-200: #e2e8f0;
  --gray-300: #cbd5e1;
  --gray-400: #94a3b8;
  --gray-500: #64748b;
  --gray-600: #475569;
  --gray-700: #334155;
  --gray-800: #1e293b;
  --gray-900: #0f172a;
  
  /* Typography */
  --font-family-primary: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-family-secondary: '<PERSON><PERSON>wal', 'Arial', sans-serif;
  
  /* Font Sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  
  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 0.75rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-8: 2rem;
  --spacing-10: 2.5rem;
  --spacing-12: 3rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  
  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-full: 9999px;
  
  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
  
  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
  
  /* Z-index */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--gray-800);
  background-color: var(--gray-50);
  direction: rtl;
  text-align: right;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family-primary);
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: var(--spacing-4);
  color: var(--gray-900);
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }

p {
  margin-bottom: var(--spacing-4);
  line-height: 1.7;
}

a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-dark);
}

/* Container */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

@media (min-width: 768px) {
  .container {
    padding: 0 var(--spacing-6);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 var(--spacing-8);
  }
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--text-base);
  font-weight: 500;
  line-height: 1;
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  white-space: nowrap;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background-color: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
  color: var(--white);
}

.btn-outline {
  background-color: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline:hover:not(:disabled) {
  background-color: var(--primary-color);
  color: var(--white);
}

.btn-secondary {
  background-color: var(--gray-200);
  color: var(--gray-800);
  border-color: var(--gray-200);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--gray-300);
  border-color: var(--gray-300);
}

.btn-sm {
  padding: var(--spacing-2) var(--spacing-4);
  font-size: var(--text-sm);
}

.btn-lg {
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--text-lg);
}

/* Form Elements */
.form-group {
  margin-bottom: var(--spacing-5);
}

.form-label {
  display: block;
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: var(--spacing-2);
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: var(--spacing-3);
  font-size: var(--text-base);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  background-color: var(--white);
  transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.form-input:disabled,
.form-select:disabled,
.form-textarea:disabled {
  background-color: var(--gray-100);
  color: var(--gray-500);
  cursor: not-allowed;
}

.form-textarea {
  resize: vertical;
  min-height: 100px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-4);
}

@media (min-width: 768px) {
  .form-row {
    grid-template-columns: 1fr 1fr;
  }
}

.form-help {
  font-size: var(--text-sm);
  color: var(--gray-500);
  margin-top: var(--spacing-1);
}

/* Checkbox and Radio */
.checkbox-label,
.radio-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  cursor: pointer;
  font-size: var(--text-sm);
}

.checkbox,
.radio {
  width: 1rem;
  height: 1rem;
  accent-color: var(--primary-color);
}

/* Loading Spinner */
.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid var(--gray-200);
  border-top: 2px solid var(--primary-color);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

.loading-spinner.small {
  width: 1rem;
  height: 1rem;
  border-width: 1px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-4);
  padding: var(--spacing-16);
  text-align: center;
}

/* Messages */
.error-message,
.success-message,
.warning-message {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-4);
  border-radius: var(--radius-md);
  font-size: var(--text-sm);
  margin-bottom: var(--spacing-4);
}

.error-message {
  background-color: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.success-message {
  background-color: #f0fdf4;
  color: #16a34a;
  border: 1px solid #bbf7d0;
}

.warning-message {
  background-color: #fffbeb;
  color: #d97706;
  border: 1px solid #fed7aa;
}

/* Cards */
.card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  overflow: hidden;
}

.card-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--gray-200);
}

.card-body {
  padding: var(--spacing-6);
}

.card-footer {
  padding: var(--spacing-6);
  border-top: 1px solid var(--gray-200);
  background-color: var(--gray-50);
}

/* Grid System */
.grid {
  display: grid;
  gap: var(--spacing-6);
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

@media (max-width: 767px) {
  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: 1fr;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.font-bold { font-weight: 700; }
.font-semibold { font-weight: 600; }
.font-medium { font-weight: 500; }

.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--secondary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-error { color: var(--error-color); }

.bg-primary { background-color: var(--primary-color); }
.bg-secondary { background-color: var(--secondary-color); }
.bg-white { background-color: var(--white); }
.bg-gray-50 { background-color: var(--gray-50); }
.bg-gray-100 { background-color: var(--gray-100); }

.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-full { border-radius: var(--radius-full); }

.shadow { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

.gap-1 { gap: var(--spacing-1); }
.gap-2 { gap: var(--spacing-2); }
.gap-3 { gap: var(--spacing-3); }
.gap-4 { gap: var(--spacing-4); }
.gap-6 { gap: var(--spacing-6); }
.gap-8 { gap: var(--spacing-8); }

.p-2 { padding: var(--spacing-2); }
.p-4 { padding: var(--spacing-4); }
.p-6 { padding: var(--spacing-6); }
.p-8 { padding: var(--spacing-8); }

.m-2 { margin: var(--spacing-2); }
.m-4 { margin: var(--spacing-4); }
.m-6 { margin: var(--spacing-6); }
.m-8 { margin: var(--spacing-8); }

.mb-2 { margin-bottom: var(--spacing-2); }
.mb-4 { margin-bottom: var(--spacing-4); }
.mb-6 { margin-bottom: var(--spacing-6); }
.mb-8 { margin-bottom: var(--spacing-8); }

.mt-2 { margin-top: var(--spacing-2); }
.mt-4 { margin-top: var(--spacing-4); }
.mt-6 { margin-top: var(--spacing-6); }
.mt-8 { margin-top: var(--spacing-8); }

/* Responsive Utilities */
@media (min-width: 768px) {
  .md\:block { display: block; }
  .md\:hidden { display: none; }
  .md\:flex { display: flex; }
  .md\:grid { display: grid; }
  .md\:grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
}

@media (min-width: 1024px) {
  .lg\:block { display: block; }
  .lg\:hidden { display: none; }
  .lg\:flex { display: flex; }
  .lg\:grid { display: grid; }
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
  .lg\:grid-cols-5 { grid-template-columns: repeat(5, 1fr); }
}

/* Header Styles */
.header {
  background-color: var(--white);
  border-bottom: 1px solid var(--gray-200);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4) 0;
}

.logo {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--primary-color);
  text-decoration: none;
}

.nav-menu {
  display: none;
  align-items: center;
  gap: var(--spacing-6);
}

@media (min-width: 768px) {
  .nav-menu {
    display: flex;
  }
}

.nav-link {
  font-weight: 500;
  color: var(--gray-700);
  transition: color var(--transition-fast);
}

.nav-link:hover {
  color: var(--primary-color);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
}

.search-box {
  display: none;
  position: relative;
}

@media (min-width: 768px) {
  .search-box {
    display: block;
  }
}

.search-input {
  width: 300px;
  padding: var(--spacing-2) var(--spacing-3);
  padding-right: var(--spacing-10);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-full);
  font-size: var(--text-sm);
}

.search-btn {
  position: absolute;
  right: var(--spacing-2);
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: var(--gray-500);
  cursor: pointer;
}

.cart-icon {
  position: relative;
  background: none;
  border: none;
  color: var(--gray-700);
  cursor: pointer;
  padding: var(--spacing-2);
}

.cart-count {
  position: absolute;
  top: 0;
  right: 0;
  background-color: var(--error-color);
  color: var(--white);
  font-size: var(--text-xs);
  font-weight: 600;
  padding: 2px 6px;
  border-radius: var(--radius-full);
  min-width: 18px;
  text-align: center;
}

.user-menu {
  position: relative;
}

.user-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  background: none;
  border: none;
  color: var(--gray-700);
  cursor: pointer;
  padding: var(--spacing-2);
}

.mobile-menu-btn {
  display: block;
  background: none;
  border: none;
  color: var(--gray-700);
  cursor: pointer;
  padding: var(--spacing-2);
}

@media (min-width: 768px) {
  .mobile-menu-btn {
    display: none;
  }
}

/* Footer Styles */
.footer {
  background-color: var(--gray-900);
  color: var(--gray-300);
  padding: var(--spacing-16) 0 var(--spacing-8);
  margin-top: var(--spacing-16);
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-8);
}

@media (min-width: 768px) {
  .footer-content {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .footer-content {
    grid-template-columns: repeat(4, 1fr);
  }
}

.footer-section h3 {
  color: var(--white);
  font-size: var(--text-lg);
  margin-bottom: var(--spacing-4);
}

.footer-links {
  list-style: none;
}

.footer-links li {
  margin-bottom: var(--spacing-2);
}

.footer-links a {
  color: var(--gray-300);
  transition: color var(--transition-fast);
}

.footer-links a:hover {
  color: var(--white);
}

.footer-bottom {
  border-top: 1px solid var(--gray-700);
  margin-top: var(--spacing-8);
  padding-top: var(--spacing-6);
  text-align: center;
  color: var(--gray-400);
  font-size: var(--text-sm);
}

/* Home Page Styles */
.hero-section {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: var(--white);
  padding: var(--spacing-20) 0;
  text-align: center;
}

.hero-title {
  font-size: var(--text-5xl);
  font-weight: 700;
  margin-bottom: var(--spacing-6);
  color: var(--white);
}

.hero-subtitle {
  font-size: var(--text-xl);
  margin-bottom: var(--spacing-8);
  opacity: 0.9;
}

.hero-cta {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  background-color: var(--white);
  color: var(--primary-color);
  padding: var(--spacing-4) var(--spacing-8);
  border-radius: var(--radius-lg);
  font-weight: 600;
  font-size: var(--text-lg);
  transition: transform var(--transition-fast);
}

.hero-cta:hover {
  transform: translateY(-2px);
  color: var(--primary-color);
}

.features-section {
  padding: var(--spacing-16) 0;
}

.section-title {
  text-align: center;
  font-size: var(--text-3xl);
  margin-bottom: var(--spacing-12);
  color: var(--gray-900);
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-8);
}

@media (min-width: 768px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

.feature-card {
  text-align: center;
  padding: var(--spacing-6);
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.feature-icon {
  width: 4rem;
  height: 4rem;
  background-color: var(--primary-color);
  color: var(--white);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-4);
}

.feature-title {
  font-size: var(--text-xl);
  font-weight: 600;
  margin-bottom: var(--spacing-3);
  color: var(--gray-900);
}

.feature-description {
  color: var(--gray-600);
  line-height: 1.6;
}

/* Product Card Styles */
.product-card {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
  position: relative;
}

.product-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

.product-image {
  position: relative;
  overflow: hidden;
  aspect-ratio: 1;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-slow);
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-badge {
  position: absolute;
  top: var(--spacing-2);
  right: var(--spacing-2);
  background-color: var(--error-color);
  color: var(--white);
  font-size: var(--text-xs);
  font-weight: 600;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
}

.product-badge.out-of-stock {
  background-color: var(--gray-500);
}

.product-info {
  padding: var(--spacing-4);
}

.product-name {
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-2);
  color: var(--gray-900);
  line-height: 1.3;
}

.product-description {
  color: var(--gray-600);
  font-size: var(--text-sm);
  margin-bottom: var(--spacing-3);
  line-height: 1.5;
}

.product-price {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-4);
}

.current-price {
  font-size: var(--text-lg);
  font-weight: 700;
  color: var(--primary-color);
}

.original-price {
  font-size: var(--text-base);
  color: var(--gray-500);
  text-decoration: line-through;
}

.add-to-cart-btn {
  width: 100%;
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  padding: var(--spacing-3);
  border-radius: var(--radius-md);
  font-weight: 500;
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.add-to-cart-btn:hover:not(:disabled) {
  background-color: var(--primary-dark);
}

.add-to-cart-btn:disabled {
  background-color: var(--gray-400);
  cursor: not-allowed;
}

/* Products Page Styles */
.products-page {
  padding: var(--spacing-8) 0;
}

.page-header {
  margin-bottom: var(--spacing-8);
}

.page-title {
  font-size: var(--text-3xl);
  margin-bottom: var(--spacing-4);
}

.page-subtitle {
  color: var(--gray-600);
  font-size: var(--text-lg);
}

.products-layout {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-8);
}

@media (min-width: 1024px) {
  .products-layout {
    grid-template-columns: 280px 1fr;
  }
}

.filters-sidebar {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-sm);
  height: fit-content;
}

.filter-section {
  margin-bottom: var(--spacing-6);
}

.filter-title {
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-4);
  color: var(--gray-900);
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.filter-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.products-main {
  min-height: 400px;
}

.products-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-6);
  padding: var(--spacing-4);
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.results-count {
  color: var(--gray-600);
  font-size: var(--text-sm);
}

.sort-select {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  background-color: var(--white);
}

.products-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);
}

@media (min-width: 640px) {
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .products-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .products-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-2);
  margin-top: var(--spacing-8);
}

.pagination-btn {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--gray-300);
  background-color: var(--white);
  color: var(--gray-700);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.pagination-btn:hover:not(:disabled) {
  background-color: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.pagination-btn.active {
  background-color: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Product Detail Page Styles */
.product-detail {
  padding: var(--spacing-8) 0;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-6);
  font-size: var(--text-sm);
  color: var(--gray-600);
}

.breadcrumb-separator {
  color: var(--gray-400);
}

.product-layout {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-8);
  margin-bottom: var(--spacing-12);
}

@media (min-width: 1024px) {
  .product-layout {
    grid-template-columns: 1fr 1fr;
  }
}

.product-gallery {
  position: relative;
}

.main-image {
  width: 100%;
  aspect-ratio: 1;
  object-fit: cover;
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-4);
}

.thumbnail-list {
  display: flex;
  gap: var(--spacing-2);
  overflow-x: auto;
}

.thumbnail {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: var(--radius-md);
  cursor: pointer;
  border: 2px solid transparent;
  transition: border-color var(--transition-fast);
}

.thumbnail.active {
  border-color: var(--primary-color);
}

.product-details {
  padding: var(--spacing-6);
}

.product-title {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin-bottom: var(--spacing-4);
  color: var(--gray-900);
}

.product-rating {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-4);
}

.stars {
  display: flex;
  gap: var(--spacing-1);
}

.star {
  color: var(--accent-color);
}

.rating-text {
  color: var(--gray-600);
  font-size: var(--text-sm);
}

.product-price-section {
  margin-bottom: var(--spacing-6);
}

.price-display {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-2);
}

.current-price {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--primary-color);
}

.original-price {
  font-size: var(--text-xl);
  color: var(--gray-500);
  text-decoration: line-through;
}

.discount-badge {
  background-color: var(--error-color);
  color: var(--white);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  font-size: var(--text-sm);
  font-weight: 600;
}

.stock-status {
  font-size: var(--text-sm);
  font-weight: 500;
}

.stock-status.in-stock {
  color: var(--success-color);
}

.stock-status.low-stock {
  color: var(--warning-color);
}

.stock-status.out-of-stock {
  color: var(--error-color);
}

.quantity-selector {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.quantity-label {
  font-weight: 500;
  color: var(--gray-700);
}

.quantity-controls {
  display: flex;
  align-items: center;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.quantity-btn {
  background-color: var(--gray-100);
  border: none;
  padding: var(--spacing-2) var(--spacing-3);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.quantity-btn:hover:not(:disabled) {
  background-color: var(--gray-200);
}

.quantity-input {
  border: none;
  padding: var(--spacing-2) var(--spacing-3);
  text-align: center;
  width: 60px;
  background-color: var(--white);
}

.product-actions {
  display: flex;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-8);
}

.add-to-cart {
  flex: 1;
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  padding: var(--spacing-4);
  border-radius: var(--radius-lg);
  font-size: var(--text-lg);
  font-weight: 600;
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.add-to-cart:hover:not(:disabled) {
  background-color: var(--primary-dark);
}

.wishlist-btn {
  background-color: var(--white);
  color: var(--gray-600);
  border: 1px solid var(--gray-300);
  padding: var(--spacing-4);
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.wishlist-btn:hover {
  background-color: var(--gray-50);
  color: var(--error-color);
}

.product-tabs {
  border-bottom: 1px solid var(--gray-200);
  margin-bottom: var(--spacing-6);
}

.tab-list {
  display: flex;
  gap: var(--spacing-6);
}

.tab-btn {
  background: none;
  border: none;
  padding: var(--spacing-4) 0;
  font-size: var(--text-base);
  font-weight: 500;
  color: var(--gray-600);
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all var(--transition-fast);
}

.tab-btn.active {
  color: var(--primary-color);
  border-bottom-color: var(--primary-color);
}

.tab-content {
  color: var(--gray-700);
  line-height: 1.7;
}

/* Cart Page Styles */
.cart-page {
  padding: var(--spacing-8) 0;
}

.cart-layout {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-8);
}

@media (min-width: 1024px) {
  .cart-layout {
    grid-template-columns: 2fr 1fr;
  }
}

.cart-items {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-sm);
}

.cart-item {
  display: grid;
  grid-template-columns: 80px 1fr auto;
  gap: var(--spacing-4);
  padding: var(--spacing-4) 0;
  border-bottom: 1px solid var(--gray-200);
}

.cart-item:last-child {
  border-bottom: none;
}

.item-image {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: var(--radius-md);
}

.item-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.item-name {
  font-weight: 600;
  color: var(--gray-900);
}

.item-price {
  color: var(--primary-color);
  font-weight: 500;
}

.item-controls {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-2);
}

.remove-btn {
  background: none;
  border: none;
  color: var(--error-color);
  cursor: pointer;
  font-size: var(--text-sm);
  transition: color var(--transition-fast);
}

.remove-btn:hover {
  color: var(--error-color);
  text-decoration: underline;
}

.cart-summary {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-sm);
  height: fit-content;
}

.summary-title {
  font-size: var(--text-xl);
  font-weight: 600;
  margin-bottom: var(--spacing-4);
  color: var(--gray-900);
}

.summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-3) 0;
  border-bottom: 1px solid var(--gray-200);
}

.summary-row:last-child {
  border-bottom: none;
}

.summary-row.total {
  font-size: var(--text-lg);
  font-weight: 700;
  color: var(--gray-900);
  border-top: 2px solid var(--gray-200);
  margin-top: var(--spacing-2);
  padding-top: var(--spacing-4);
}

.summary-divider {
  height: 1px;
  background-color: var(--gray-200);
  margin: var(--spacing-4) 0;
}

.checkout-btn {
  width: 100%;
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  padding: var(--spacing-4);
  border-radius: var(--radius-lg);
  font-size: var(--text-lg);
  font-weight: 600;
  cursor: pointer;
  transition: background-color var(--transition-fast);
  margin-top: var(--spacing-4);
}

.checkout-btn:hover:not(:disabled) {
  background-color: var(--primary-dark);
}

.empty-cart {
  text-align: center;
  padding: var(--spacing-16);
  background-color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
}

.empty-cart-icon {
  font-size: 4rem;
  color: var(--gray-400);
  margin-bottom: var(--spacing-4);
}

.empty-cart-title {
  font-size: var(--text-2xl);
  font-weight: 600;
  margin-bottom: var(--spacing-2);
  color: var(--gray-900);
}

.empty-cart-text {
  color: var(--gray-600);
  margin-bottom: var(--spacing-6);
}

/* Checkout Page Styles */
.checkout-page {
  padding: var(--spacing-8) 0;
}

.checkout-steps {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-8);
}

.step {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  color: var(--gray-500);
}

.step.active {
  color: var(--primary-color);
}

.step.completed {
  color: var(--success-color);
}

.step-number {
  width: 2rem;
  height: 2rem;
  border-radius: var(--radius-full);
  background-color: var(--gray-200);
  color: var(--gray-600);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: var(--text-sm);
}

.step.active .step-number {
  background-color: var(--primary-color);
  color: var(--white);
}

.step.completed .step-number {
  background-color: var(--success-color);
  color: var(--white);
}

.step-label {
  font-weight: 500;
  font-size: var(--text-sm);
}

.checkout-content {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-8);
}

@media (min-width: 1024px) {
  .checkout-content {
    grid-template-columns: 2fr 1fr;
  }
}

.checkout-main {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-sm);
}

.checkout-step {
  margin-bottom: var(--spacing-6);
}

.step-title {
  font-size: var(--text-2xl);
  font-weight: 600;
  margin-bottom: var(--spacing-6);
  color: var(--gray-900);
}

.form-section {
  margin-bottom: var(--spacing-6);
}

.step-actions {
  display: flex;
  justify-content: space-between;
  gap: var(--spacing-4);
  margin-top: var(--spacing-8);
}

.payment-methods {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.payment-option {
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: border-color var(--transition-fast);
}

.payment-option:has(input:checked) {
  border-color: var(--primary-color);
  background-color: var(--primary-color);
  background-color: rgb(37 99 235 / 0.05);
}

.payment-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4);
  cursor: pointer;
}

.payment-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.payment-title {
  font-weight: 600;
  color: var(--gray-900);
}

.payment-description {
  font-size: var(--text-sm);
  color: var(--gray-600);
}

.payment-icons {
  font-size: var(--text-2xl);
}

.checkout-sidebar {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-sm);
  height: fit-content;
}

.order-summary {
  margin-bottom: var(--spacing-6);
}

.order-items {
  margin-bottom: var(--spacing-6);
}

.order-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3) 0;
  border-bottom: 1px solid var(--gray-200);
}

.order-item:last-child {
  border-bottom: none;
}

.order-item .item-image {
  position: relative;
  width: 60px;
  height: 60px;
}

.order-item .item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: var(--radius-md);
}

.item-quantity {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: var(--primary-color);
  color: var(--white);
  font-size: var(--text-xs);
  font-weight: 600;
  padding: 2px 6px;
  border-radius: var(--radius-full);
  min-width: 20px;
  text-align: center;
}

.order-item .item-details {
  flex: 1;
}

.order-item .item-name {
  font-weight: 500;
  color: var(--gray-900);
  font-size: var(--text-sm);
  margin-bottom: var(--spacing-1);
}

.order-item .item-price {
  color: var(--primary-color);
  font-weight: 600;
  font-size: var(--text-sm);
}

.summary-totals {
  border-top: 1px solid var(--gray-200);
  padding-top: var(--spacing-4);
}

.order-review {
  margin-bottom: var(--spacing-6);
}

.review-section {
  margin-bottom: var(--spacing-6);
  padding-bottom: var(--spacing-4);
  border-bottom: 1px solid var(--gray-200);
}

.review-section:last-child {
  border-bottom: none;
}

.review-section h3 {
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-3);
  color: var(--gray-900);
}

.review-content {
  color: var(--gray-700);
  line-height: 1.6;
}

.review-content p {
  margin-bottom: var(--spacing-2);
}

.review-content p:last-child {
  margin-bottom: 0;
}

/* Admin Dashboard Styles */
.admin-dashboard {
  padding: var(--spacing-8) 0;
  min-height: 100vh;
}

.admin-header {
  margin-bottom: var(--spacing-8);
  text-align: center;
}

.admin-title {
  font-size: var(--text-3xl);
  font-weight: 700;
  margin-bottom: var(--spacing-2);
  color: var(--gray-900);
}

.admin-subtitle {
  color: var(--gray-600);
  font-size: var(--text-lg);
}

.admin-layout {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-8);
}

@media (min-width: 1024px) {
  .admin-layout {
    grid-template-columns: 280px 1fr;
  }
}

.admin-sidebar {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-sm);
  height: fit-content;
}

.admin-nav {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.nav-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3) var(--spacing-4);
  border: none;
  background: none;
  color: var(--gray-700);
  font-size: var(--text-base);
  font-weight: 500;
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-align: right;
  width: 100%;
}

.nav-item:hover {
  background-color: var(--gray-100);
  color: var(--primary-color);
}

.nav-item.active {
  background-color: var(--primary-color);
  color: var(--white);
}

.admin-main {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-sm);
  min-height: 600px;
}

.dashboard-content {
  margin-bottom: var(--spacing-8);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-6);
  margin-bottom: var(--spacing-8);
}

@media (min-width: 640px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.stat-card {
  background-color: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.stat-icon {
  width: 3rem;
  height: 3rem;
  background-color: var(--primary-color);
  color: var(--white);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: var(--text-2xl);
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.stat-label {
  color: var(--gray-600);
  font-size: var(--text-sm);
}

.dashboard-charts {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-6);
}

@media (min-width: 1024px) {
  .dashboard-charts {
    grid-template-columns: repeat(2, 1fr);
  }
}

.chart-card {
  background-color: var(--gray-50);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
}

.chart-card h3 {
  font-size: var(--text-lg);
  font-weight: 600;
  margin-bottom: var(--spacing-4);
  color: var(--gray-900);
}

.recent-orders,
.low-stock-products {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.order-item,
.stock-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-3);
  background-color: var(--white);
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-200);
}

.order-info,
.product-info {
  flex: 1;
}

.order-id,
.product-name {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.order-customer,
.product-sku {
  color: var(--gray-600);
  font-size: var(--text-sm);
}

.order-details {
  text-align: left;
}

.order-total {
  font-weight: 600;
  color: var(--primary-color);
  margin-bottom: var(--spacing-1);
}

.order-status {
  font-size: var(--text-xs);
  font-weight: 600;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  text-transform: uppercase;
}

.order-status.pending {
  background-color: #fef3c7;
  color: #d97706;
}

.order-status.processing {
  background-color: #dbeafe;
  color: #2563eb;
}

.order-status.shipped {
  background-color: #d1fae5;
  color: #059669;
}

.order-status.delivered {
  background-color: #dcfce7;
  color: #16a34a;
}

.order-status.cancelled {
  background-color: #fee2e2;
  color: #dc2626;
}

.stock-quantity {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
}

.stock-count {
  font-weight: 700;
  font-size: var(--text-lg);
  color: var(--warning-color);
}

.stock-label {
  color: var(--gray-600);
  font-size: var(--text-sm);
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-6);
}

.content-header h2 {
  font-size: var(--text-2xl);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 0;
}

.filters {
  display: flex;
  gap: var(--spacing-4);
}

.filter-select {
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  background-color: var(--white);
  font-size: var(--text-sm);
}

.data-table {
  background-color: var(--white);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  background-color: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
  font-weight: 600;
  color: var(--gray-900);
  font-size: var(--text-sm);
}

.table-body {
  display: flex;
  flex-direction: column;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr 1fr;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  border-bottom: 1px solid var(--gray-200);
  align-items: center;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background-color: var(--gray-50);
}

.col {
  font-size: var(--text-sm);
}

.product-col {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.product-thumb {
  width: 50px;
  height: 50px;
  object-fit: cover;
  border-radius: var(--radius-md);
}

.product-details {
  flex: 1;
}

.product-name {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-1);
}

.product-sku {
  color: var(--gray-600);
  font-size: var(--text-xs);
}

.current-price {
  font-weight: 600;
  color: var(--primary-color);
}

.original-price {
  color: var(--gray-500);
  text-decoration: line-through;
  font-size: var(--text-xs);
  margin-right: var(--spacing-2);
}

.stock {
  font-weight: 600;
}

.stock.low {
  color: var(--warning-color);
}

.status {
  font-size: var(--text-xs);
  font-weight: 600;
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-sm);
  text-transform: uppercase;
}

.status.active {
  background-color: #dcfce7;
  color: #16a34a;
}

.status.inactive {
  background-color: #fee2e2;
  color: #dc2626;
}

.actions-col {
  display: flex;
  gap: var(--spacing-2);
}

.action-btn {
  background: none;
  border: 1px solid var(--gray-300);
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--gray-600);
}

.action-btn:hover {
  background-color: var(--gray-100);
}

.action-btn.edit:hover {
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.action-btn.delete:hover {
  color: var(--error-color);
  border-color: var(--error-color);
}

.action-btn.view:hover {
  color: var(--success-color);
  border-color: var(--success-color);
}

/* Authentication Pages Styles */
.auth-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  padding: var(--spacing-4);
}

.auth-container {
  background-color: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  padding: var(--spacing-8);
  width: 100%;
  max-width: 400px;
}

.auth-header {
  text-align: center;
  margin-bottom: var(--spacing-8);
}

.auth-logo {
  font-size: var(--text-3xl);
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-4);
}

.auth-title {
  font-size: var(--text-2xl);
  font-weight: 600;
  margin-bottom: var(--spacing-2);
  color: var(--gray-900);
}

.auth-subtitle {
  color: var(--gray-600);
  font-size: var(--text-base);
}

.auth-form {
  margin-bottom: var(--spacing-6);
}

.auth-form .form-group {
  margin-bottom: var(--spacing-5);
}

.auth-form .form-input {
  padding: var(--spacing-4);
  font-size: var(--text-base);
  border-radius: var(--radius-lg);
}

.auth-form .btn {
  width: 100%;
  padding: var(--spacing-4);
  font-size: var(--text-lg);
  font-weight: 600;
  border-radius: var(--radius-lg);
}

.auth-divider {
  display: flex;
  align-items: center;
  margin: var(--spacing-6) 0;
}

.auth-divider::before,
.auth-divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background-color: var(--gray-300);
}

.auth-divider span {
  padding: 0 var(--spacing-4);
  color: var(--gray-500);
  font-size: var(--text-sm);
}

.social-login {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-6);
}

.social-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3);
  border: 1px solid var(--gray-300);
  background-color: var(--white);
  color: var(--gray-700);
  border-radius: var(--radius-lg);
  font-weight: 500;
  cursor: pointer;
  transition: all var(--transition-fast);
}

.social-btn:hover {
  background-color: var(--gray-50);
  border-color: var(--gray-400);
}

.auth-footer {
  text-align: center;
  color: var(--gray-600);
  font-size: var(--text-sm);
}

.auth-footer a {
  color: var(--primary-color);
  font-weight: 500;
}

.forgot-password {
  text-align: center;
  margin-top: var(--spacing-4);
}

.forgot-password a {
  color: var(--primary-color);
  font-size: var(--text-sm);
  font-weight: 500;
}

/* Profile Page Styles */
.profile-page {
  padding: var(--spacing-8) 0;
}

.profile-layout {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-8);
}

@media (min-width: 1024px) {
  .profile-layout {
    grid-template-columns: 300px 1fr;
  }
}

.profile-sidebar {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-sm);
  height: fit-content;
}

.profile-avatar {
  text-align: center;
  margin-bottom: var(--spacing-6);
}

.avatar-image {
  width: 100px;
  height: 100px;
  border-radius: var(--radius-full);
  object-fit: cover;
  margin-bottom: var(--spacing-4);
  border: 4px solid var(--gray-200);
}

.avatar-placeholder {
  width: 100px;
  height: 100px;
  border-radius: var(--radius-full);
  background-color: var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-4);
  font-size: var(--text-2xl);
  color: var(--gray-500);
}

.profile-name {
  font-size: var(--text-xl);
  font-weight: 600;
  margin-bottom: var(--spacing-2);
  color: var(--gray-900);
}

.profile-email {
  color: var(--gray-600);
  font-size: var(--text-sm);
}

.profile-menu {
  list-style: none;
  margin-top: var(--spacing-6);
}

.profile-menu li {
  margin-bottom: var(--spacing-2);
}

.profile-menu a {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3);
  color: var(--gray-700);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.profile-menu a:hover,
.profile-menu a.active {
  background-color: var(--primary-color);
  color: var(--white);
}

.profile-main {
  background-color: var(--white);
  border-radius: var(--radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--shadow-sm);
}

.profile-section {
  margin-bottom: var(--spacing-8);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-6);
}

.section-header h2 {
  font-size: var(--text-xl);
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: 0;
}

/* Responsive Design Improvements */
@media (max-width: 767px) {
  .container {
    padding: 0 var(--spacing-3);
  }

  .hero-title {
    font-size: var(--text-3xl);
  }

  .hero-subtitle {
    font-size: var(--text-lg);
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .products-layout {
    grid-template-columns: 1fr;
  }

  .filters-sidebar {
    order: 2;
  }

  .products-main {
    order: 1;
  }

  .product-layout {
    grid-template-columns: 1fr;
  }

  .cart-layout {
    grid-template-columns: 1fr;
  }

  .checkout-content {
    grid-template-columns: 1fr;
  }

  .checkout-sidebar {
    order: 1;
  }

  .checkout-main {
    order: 2;
  }

  .admin-layout {
    grid-template-columns: 1fr;
  }

  .admin-sidebar {
    order: 2;
  }

  .admin-main {
    order: 1;
  }

  .profile-layout {
    grid-template-columns: 1fr;
  }

  .profile-sidebar {
    order: 2;
  }

  .profile-main {
    order: 1;
  }

  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: var(--spacing-2);
  }

  .table-header {
    display: none;
  }

  .table-row {
    display: block;
    padding: var(--spacing-4);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-3);
  }

  .col {
    display: block;
    margin-bottom: var(--spacing-2);
  }

  .col::before {
    content: attr(data-label) ': ';
    font-weight: 600;
    color: var(--gray-700);
  }

  .product-col::before {
    display: none;
  }

  .actions-col {
    justify-content: flex-start;
    margin-top: var(--spacing-3);
  }

  .step-actions {
    flex-direction: column;
  }

  .step-actions .btn {
    width: 100%;
  }
}

/* Print Styles */
@media print {
  .header,
  .footer,
  .admin-sidebar,
  .filters-sidebar,
  .checkout-sidebar {
    display: none;
  }

  .container {
    max-width: none;
    padding: 0;
  }

  .admin-layout,
  .products-layout,
  .cart-layout,
  .checkout-content,
  .profile-layout {
    grid-template-columns: 1fr;
  }

  .card,
  .product-card,
  .feature-card {
    box-shadow: none;
    border: 1px solid var(--gray-300);
  }
}

/* Dark Mode Support (Optional) */
@media (prefers-color-scheme: dark) {
  :root {
    --white: #1f2937;
    --gray-50: #111827;
    --gray-100: #1f2937;
    --gray-200: #374151;
    --gray-300: #4b5563;
    --gray-400: #6b7280;
    --gray-500: #9ca3af;
    --gray-600: #d1d5db;
    --gray-700: #e5e7eb;
    --gray-800: #f3f4f6;
    --gray-900: #ffffff;
  }

  body {
    background-color: var(--gray-50);
    color: var(--gray-800);
  }
}
