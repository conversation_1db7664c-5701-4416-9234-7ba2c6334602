import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from 'react-query';
import '@testing-library/jest-dom';

import App from '../App';
import { AuthProvider } from '../contexts/AuthContext';
import { CartProvider } from '../contexts/CartContext';

// Mock Supabase
jest.mock('../lib/supabase', () => ({
  supabase: {
    auth: {
      getSession: jest.fn(() => Promise.resolve({ data: { session: null }, error: null })),
      onAuthStateChange: jest.fn(() => ({ data: { subscription: { unsubscribe: jest.fn() } } })),
      signUp: jest.fn(),
      signInWithPassword: jest.fn(),
      signOut: jest.fn(),
      updateUser: jest.fn(),
    },
    from: jest.fn(() => ({
      select: jest.fn(() => ({
        eq: jest.fn(() => ({
          single: jest.fn(() => Promise.resolve({ data: null, error: null })),
          order: jest.fn(() => Promise.resolve({ data: [], error: null })),
        })),
        order: jest.fn(() => Promise.resolve({ data: [], error: null })),
        range: jest.fn(() => Promise.resolve({ data: [], error: null })),
      })),
      insert: jest.fn(() => Promise.resolve({ data: null, error: null })),
      update: jest.fn(() => Promise.resolve({ data: null, error: null })),
      delete: jest.fn(() => Promise.resolve({ data: null, error: null })),
    })),
  },
}));

// Mock API calls
jest.mock('../services/api', () => ({
  authAPI: {
    register: jest.fn(() => Promise.resolve({ data: { success: true } })),
    login: jest.fn(() => Promise.resolve({ data: { success: true } })),
    logout: jest.fn(() => Promise.resolve({ data: { success: true } })),
    getProfile: jest.fn(() => Promise.resolve({ data: { success: true, data: {} } })),
    updateProfile: jest.fn(() => Promise.resolve({ data: { success: true } })),
  },
  productsAPI: {
    getProducts: jest.fn(() => Promise.resolve({ 
      data: { 
        success: true, 
        data: { 
          products: [
            {
              id: '1',
              name_ar: 'منتج تجريبي',
              name_en: 'Test Product',
              description_ar: 'وصف المنتج',
              price: 100,
              sale_price: 80,
              stock_quantity: 10,
              images: ['/test-image.jpg'],
              is_active: true,
              category_id: '1'
            }
          ],
          total: 1,
          page: 1,
          limit: 12
        }
      }
    })),
    getProduct: jest.fn(() => Promise.resolve({ 
      data: { 
        success: true, 
        data: {
          id: '1',
          name_ar: 'منتج تجريبي',
          name_en: 'Test Product',
          description_ar: 'وصف المنتج',
          price: 100,
          sale_price: 80,
          stock_quantity: 10,
          images: ['/test-image.jpg'],
          is_active: true,
          category_id: '1'
        }
      }
    })),
  },
  categoriesAPI: {
    getCategories: jest.fn(() => Promise.resolve({ 
      data: { 
        success: true, 
        data: [
          { id: '1', name_ar: 'فئة تجريبية', name_en: 'Test Category' }
        ]
      }
    })),
  },
  cartAPI: {
    getCart: jest.fn(() => Promise.resolve({ data: { success: true, data: [] } })),
    addToCart: jest.fn(() => Promise.resolve({ data: { success: true } })),
    updateCartItem: jest.fn(() => Promise.resolve({ data: { success: true } })),
    removeFromCart: jest.fn(() => Promise.resolve({ data: { success: true } })),
    clearCart: jest.fn(() => Promise.resolve({ data: { success: true } })),
  },
  ordersAPI: {
    createOrder: jest.fn(() => Promise.resolve({ 
      data: { 
        success: true, 
        data: { order: { id: '1' } }
      }
    })),
    getOrders: jest.fn(() => Promise.resolve({ data: { success: true, data: [] } })),
  },
  adminAPI: {
    getDashboardStats: jest.fn(() => Promise.resolve({ 
      data: { 
        success: true, 
        data: {
          total_orders: 0,
          total_products: 0,
          total_users: 0,
          total_revenue: 0,
          recent_orders: [],
          low_stock_products: []
        }
      }
    })),
    getProducts: jest.fn(() => Promise.resolve({ data: { success: true, data: { products: [] } } })),
    getOrders: jest.fn(() => Promise.resolve({ data: { success: true, data: { orders: [] } } })),
  },
}));

// Test wrapper component
const TestWrapper = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <AuthProvider>
          <CartProvider>
            {children}
          </CartProvider>
        </AuthProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('E-commerce Application Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('App Component', () => {
    test('renders without crashing', () => {
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );
      expect(document.querySelector('.app')).toBeInTheDocument();
    });

    test('has RTL direction', () => {
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );
      expect(document.querySelector('.app')).toHaveAttribute('dir', 'rtl');
    });

    test('renders header and footer', () => {
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );
      expect(document.querySelector('header')).toBeInTheDocument();
      expect(document.querySelector('footer')).toBeInTheDocument();
    });
  });

  describe('Navigation', () => {
    test('navigates to products page', async () => {
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );
      
      // Simulate navigation to products page
      window.history.pushState({}, 'Products', '/products');
      
      await waitFor(() => {
        expect(window.location.pathname).toBe('/products');
      });
    });

    test('navigates to cart page', async () => {
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );
      
      window.history.pushState({}, 'Cart', '/cart');
      
      await waitFor(() => {
        expect(window.location.pathname).toBe('/cart');
      });
    });
  });

  describe('Authentication Flow', () => {
    test('navigates to login page', async () => {
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );
      
      window.history.pushState({}, 'Login', '/login');
      
      await waitFor(() => {
        expect(window.location.pathname).toBe('/login');
      });
    });

    test('navigates to register page', async () => {
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );
      
      window.history.pushState({}, 'Register', '/register');
      
      await waitFor(() => {
        expect(window.location.pathname).toBe('/register');
      });
    });
  });

  describe('E-commerce Functionality', () => {
    test('navigates to product detail page', async () => {
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );
      
      window.history.pushState({}, 'Product Detail', '/product/1');
      
      await waitFor(() => {
        expect(window.location.pathname).toBe('/product/1');
      });
    });

    test('navigates to checkout page', async () => {
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );
      
      window.history.pushState({}, 'Checkout', '/checkout');
      
      await waitFor(() => {
        expect(window.location.pathname).toBe('/checkout');
      });
    });
  });

  describe('Admin Dashboard', () => {
    test('navigates to admin dashboard', async () => {
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );
      
      window.history.pushState({}, 'Admin', '/admin');
      
      await waitFor(() => {
        expect(window.location.pathname).toBe('/admin');
      });
    });
  });

  describe('Responsive Design', () => {
    test('applies mobile styles on small screens', () => {
      // Mock window.matchMedia for mobile
      Object.defineProperty(window, 'matchMedia', {
        writable: true,
        value: jest.fn().mockImplementation(query => ({
          matches: query === '(max-width: 767px)',
          media: query,
          onchange: null,
          addListener: jest.fn(),
          removeListener: jest.fn(),
          addEventListener: jest.fn(),
          removeEventListener: jest.fn(),
          dispatchEvent: jest.fn(),
        })),
      });

      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );

      expect(document.querySelector('.app')).toBeInTheDocument();
    });
  });

  describe('Arabic Localization', () => {
    test('displays Arabic text correctly', () => {
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );
      
      // Check if RTL direction is applied
      expect(document.querySelector('.app')).toHaveAttribute('dir', 'rtl');
    });

    test('uses Arabic fonts', () => {
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );
      
      const styles = window.getComputedStyle(document.body);
      expect(styles.fontFamily).toContain('Cairo');
    });
  });

  describe('Error Handling', () => {
    test('handles API errors gracefully', async () => {
      // Mock API error
      const { productsAPI } = require('../services/api');
      productsAPI.getProducts.mockRejectedValueOnce(new Error('API Error'));

      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );

      // Navigate to products page to trigger API call
      window.history.pushState({}, 'Products', '/products');
      
      // Should not crash the app
      expect(document.querySelector('.app')).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    test('loads quickly', () => {
      const startTime = performance.now();
      
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );
      
      const endTime = performance.now();
      const loadTime = endTime - startTime;
      
      // Should load within 1 second
      expect(loadTime).toBeLessThan(1000);
    });
  });

  describe('Accessibility', () => {
    test('has proper semantic HTML structure', () => {
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );
      
      expect(document.querySelector('header')).toBeInTheDocument();
      expect(document.querySelector('main')).toBeInTheDocument();
      expect(document.querySelector('footer')).toBeInTheDocument();
    });

    test('supports keyboard navigation', () => {
      render(
        <TestWrapper>
          <App />
        </TestWrapper>
      );
      
      // Check if focusable elements exist
      const focusableElements = document.querySelectorAll('button, a, input, select, textarea');
      expect(focusableElements.length).toBeGreaterThan(0);
    });
  });
});

// Integration Tests
describe('Integration Tests', () => {
  test('complete user journey: browse products → add to cart → checkout', async () => {
    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    );

    // 1. Navigate to products
    window.history.pushState({}, 'Products', '/products');
    await waitFor(() => {
      expect(window.location.pathname).toBe('/products');
    });

    // 2. Navigate to cart
    window.history.pushState({}, 'Cart', '/cart');
    await waitFor(() => {
      expect(window.location.pathname).toBe('/cart');
    });

    // 3. Navigate to checkout
    window.history.pushState({}, 'Checkout', '/checkout');
    await waitFor(() => {
      expect(window.location.pathname).toBe('/checkout');
    });
  });

  test('admin workflow: login → dashboard → manage products', async () => {
    render(
      <TestWrapper>
        <App />
      </TestWrapper>
    );

    // 1. Navigate to login
    window.history.pushState({}, 'Login', '/login');
    await waitFor(() => {
      expect(window.location.pathname).toBe('/login');
    });

    // 2. Navigate to admin dashboard
    window.history.pushState({}, 'Admin', '/admin');
    await waitFor(() => {
      expect(window.location.pathname).toBe('/admin');
    });
  });
});
